import React from 'react';
import { XMarkIcon, CalendarIcon } from '@heroicons/react/24/outline';

const SlotsListModal = ({ isOpen, onClose, slots }) => {
    if (!isOpen) return null;

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            case 'completed': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                        <CalendarIcon className="h-5 w-5 text-blue-600" />
                        All Slots ({slots.length})
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <div className="max-h-96 overflow-y-auto">
                    {slots.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                            <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>No slots found</p>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {slots.map((slot) => (
                                <div key={slot.id} className="bg-gray-50 p-4 rounded-lg border">
                                    <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-3 mb-2">
                                                <h4 className="font-semibold text-gray-900">{slot.activity_name}</h4>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(slot.status)}`}>
                                                    {slot.status.charAt(0).toUpperCase() + slot.status.slice(1)}
                                                </span>
                                            </div>
                                            
                                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                                <div>
                                                    <p className="text-gray-600">Date & Time</p>
                                                    <p className="font-medium">{slot.date}</p>
                                                    <p className="text-gray-500">{slot.time}</p>
                                                </div>
                                                
                                                <div>
                                                    <p className="text-gray-600">Capacity</p>
                                                    <p className="font-medium">{slot.booked_count}/{slot.capacity}</p>
                                                    <p className="text-gray-500">{slot.available_count} available</p>
                                                </div>
                                                
                                                <div>
                                                    <p className="text-gray-600">Revenue</p>
                                                    <p className="font-medium">₹{slot.collected_amount.toLocaleString()}</p>
                                                    <p className="text-gray-500">of ₹{slot.total_amount.toLocaleString()}</p>
                                                </div>
                                                
                                                <div>
                                                    <p className="text-gray-600">Commission</p>
                                                    <p className="font-medium">₹{slot.commission_amount.toLocaleString()}</p>
                                                    <p className="text-gray-500">{slot.participants.length} bookings</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div className="mt-6 flex justify-end">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SlotsListModal;
