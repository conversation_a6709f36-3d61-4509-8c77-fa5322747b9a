import React, { useState } from 'react';
import { CurrencyRupeeIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const ElephantaStaffSettings = () => {
    const [commissionAmount, setCommissionAmount] = useState(350);
    const [loading, setLoading] = useState(false);

    const handleSaveCommission = async () => {
        if (!commissionAmount || commissionAmount <= 0) {
            toast.error('Please enter a valid commission amount');
            return;
        }

        setLoading(true);
        try {
            // Update the commission amount in localStorage for demo
            const userData = localStorage.getItem('activity_crm_user');
            if (userData) {
                const user = JSON.parse(userData);
                if (user.role === 'admin' || user.role === 'manager') {
                    // In a real app, this would be an API call
                    // For demo, we'll just show success message
                    toast.success(`Commission amount updated to ₹${commissionAmount} per booking`);
                }
            }
        } catch (error) {
            toast.error('Failed to update commission amount');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center mb-4">
                <div className="p-2 bg-purple-100 rounded-lg mr-3">
                    <CurrencyRupeeIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                    <h3 className="text-lg font-semibold text-gray-900">Elephanta Staff Commission</h3>
                    <p className="text-sm text-gray-600">Set fixed commission amount per booking</p>
                </div>
            </div>

            <div className="space-y-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Commission Amount per Booking (₹)
                    </label>
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 sm:text-sm">₹</span>
                        </div>
                        <input
                            type="number"
                            value={commissionAmount}
                            onChange={(e) => setCommissionAmount(parseInt(e.target.value) || 0)}
                            className="block w-full pl-7 pr-12 border border-gray-300 rounded-md py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                            placeholder="350"
                            min="0"
                            step="1"
                        />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                        This amount will be paid to Elephanta staff for each booking they process
                    </p>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">Commission Examples</h4>
                    <div className="space-y-1 text-sm text-blue-800">
                        <div className="flex justify-between">
                            <span>1 booking (₹3,500):</span>
                            <span className="font-medium">₹{commissionAmount} commission</span>
                        </div>
                        <div className="flex justify-between">
                            <span>5 bookings (₹17,500):</span>
                            <span className="font-medium">₹{commissionAmount * 5} commission</span>
                        </div>
                        <div className="flex justify-between">
                            <span>10 bookings (₹35,000):</span>
                            <span className="font-medium">₹{commissionAmount * 10} commission</span>
                        </div>
                    </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="text-sm text-yellow-800">
                        <strong>Note:</strong> Fixed commission amounts are easier to manage than percentages, 
                        especially for cash payments. Staff will receive exactly ₹{commissionAmount} for each 
                        booking they process, regardless of the total amount collected.
                    </div>
                </div>

                <div className="flex justify-end">
                    <button
                        onClick={handleSaveCommission}
                        disabled={loading}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center gap-2"
                    >
                        {loading ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                Saving...
                            </>
                        ) : (
                            <>
                                <CheckCircleIcon className="h-4 w-4" />
                                Save Commission Amount
                            </>
                        )}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ElephantaStaffSettings;
