import React, { useState, useEffect } from 'react';
import walletService from '../../services/walletService';
import PaymentModal from './PaymentModal';
import { 
    CurrencyDollarIcon,
    BanknotesIcon,
    ClockIcon,
    UserIcon
} from '@heroicons/react/24/outline';

const StaffWallets = ({ onPaymentSuccess, canProcessPayments = true }) => {
    const [staffWallets, setStaffWallets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedWallet, setSelectedWallet] = useState(null);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [summary, setSummary] = useState(null);

    useEffect(() => {
        fetchStaffWallets();
    }, []);

    const fetchStaffWallets = async () => {
        try {
            setLoading(true);
            const response = await walletService.getStaffWallets();
            setStaffWallets(response.staff_wallets);
            setSummary({
                total_staff: response.total_staff,
                total_balance: response.total_balance,
                total_pending: response.total_pending
            });
        } catch (error) {
            console.error('Failed to fetch staff wallets:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleReleasePayment = (wallet) => {
        if (wallet.balance <= 0) {
            alert('No balance available for payment');
            return;
        }
        setSelectedWallet(wallet);
        setShowPaymentModal(true);
    };

    const handlePaymentSuccess = () => {
        setShowPaymentModal(false);
        setSelectedWallet(null);
        fetchStaffWallets();
        if (onPaymentSuccess) {
            onPaymentSuccess();
        }
        alert('Payment released successfully!');
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Summary */}
            {summary && (
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="flex items-center">
                            <UserIcon className="h-6 w-6 text-blue-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-blue-900">Total Staff</p>
                                <p className="text-2xl font-bold text-blue-600">{summary.total_staff}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                        <div className="flex items-center">
                            <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-green-900">Total Balance</p>
                                <p className="text-2xl font-bold text-green-600">
                                    ₹{summary.total_balance?.toLocaleString() || '0'}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-orange-50 p-4 rounded-lg">
                        <div className="flex items-center">
                            <ClockIcon className="h-6 w-6 text-orange-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-orange-900">Available for Payment</p>
                                <p className="text-2xl font-bold text-orange-600">
                                    ₹{summary.total_balance?.toLocaleString() || '0'}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Staff Wallets Table */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <div>
                            <h3 className="text-lg leading-6 font-medium text-gray-900">
                                Staff Wallets ({staffWallets.length})
                            </h3>
                            <p className="mt-1 max-w-2xl text-sm text-gray-500">
                                Manage staff commission payments and wallet balances
                            </p>
                        </div>
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Demo Data
                        </span>
                    </div>
                </div>

                {staffWallets.length === 0 ? (
                    <div className="text-center py-12">
                        <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No staff found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            Add staff members to see their wallets here.
                        </p>
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Staff Details
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Current Balance
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total Earned
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total Paid
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Last Transaction
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {staffWallets.map((wallet) => (
                                    <tr key={wallet.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="flex-shrink-0 h-10 w-10">
                                                    <div className="h-10 w-10 rounded-full bg-teal-100 flex items-center justify-center">
                                                        <span className="text-sm font-medium text-teal-700">
                                                            {wallet.name.charAt(0).toUpperCase()}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {wallet.name}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {wallet.email}
                                                    </div>
                                                    <div className="text-xs text-gray-400">
                                                        ID: {wallet.employee_id}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <CurrencyDollarIcon className="h-5 w-5 text-green-500 mr-2" />
                                                <div>
                                                    <div className={`text-lg font-bold ${
                                                        wallet.balance > 0 ? 'text-green-600' : 'text-gray-500'
                                                    }`}>
                                                        ₹{wallet.balance?.toLocaleString() || '0'}
                                                    </div>
                                                    {wallet.balance > 0 && (
                                                        <div className="text-xs text-green-600">Available</div>
                                                    )}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ₹{wallet.total_earned?.toLocaleString() || '0'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ₹{wallet.total_paid?.toLocaleString() || '0'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {wallet.last_transaction_at ? 
                                                new Date(wallet.last_transaction_at).toLocaleDateString() : 
                                                'No transactions'
                                            }
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            {canProcessPayments ? (
                                                <button
                                                    onClick={() => handleReleasePayment(wallet)}
                                                    disabled={wallet.balance <= 0}
                                                    className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white transition-colors duration-200 ${
                                                        wallet.balance > 0
                                                            ? 'bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 shadow-sm'
                                                            : 'bg-gray-300 cursor-not-allowed'
                                                    }`}
                                                >
                                                    <BanknotesIcon className="h-4 w-4 mr-2" />
                                                    Release Payment
                                                </button>
                                            ) : (
                                                <span className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
                                                    <BanknotesIcon className="h-4 w-4 mr-2" />
                                                    Access Restricted
                                                </span>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Payment Modal */}
            {showPaymentModal && selectedWallet && (
                <PaymentModal
                    wallet={selectedWallet}
                    personnelType="staff"
                    onSuccess={handlePaymentSuccess}
                    onClose={() => {
                        setShowPaymentModal(false);
                        setSelectedWallet(null);
                    }}
                />
            )}
        </div>
    );
};

export default StaffWallets;