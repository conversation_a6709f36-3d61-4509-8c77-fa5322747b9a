<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Activity CRM Dashboard - Manage bookings, personnel, and wallet payments" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>Activity CRM Dashboard</title>

    <!-- Custom Styles -->
    <style>
      /* Loading animation */
      .spinner {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: #14b8a6;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #0d9488;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <!-- Loading fallback -->
    <div id="loading" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);">
      <div class="text-center">
        <div class="spinner rounded-full h-32 w-32 border-b-2 border-teal-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading Activity CRM...</p>
      </div>
    </div>
  </body>
</html>