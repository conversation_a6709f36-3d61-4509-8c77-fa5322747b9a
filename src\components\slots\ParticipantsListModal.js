import React from 'react';
import { XMarkIcon, UsersIcon } from '@heroicons/react/24/outline';

const ParticipantsListModal = ({ isOpen, onClose, slots }) => {
    if (!isOpen) return null;

    // Get all participants from all slots
    const allParticipants = slots.flatMap(slot => 
        slot.participants.map(participant => ({
            ...participant,
            slot_info: {
                id: slot.id,
                activity_name: slot.activity_name,
                date: slot.date,
                time: slot.time,
                status: slot.status
            }
        }))
    );

    const getPaymentStatusColor = (status) => {
        switch (status) {
            case 'collected': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'refunded': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getSlotStatusColor = (status) => {
        switch (status) {
            case 'active': return 'text-green-600';
            case 'cancelled': return 'text-red-600';
            case 'completed': return 'text-blue-600';
            default: return 'text-gray-600';
        }
    };

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-full max-w-5xl shadow-lg rounded-md bg-white">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                        <UsersIcon className="h-5 w-5 text-green-600" />
                        All Participants ({allParticipants.length})
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <div className="max-h-96 overflow-y-auto">
                    {allParticipants.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                            <UsersIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                            <p>No participants found</p>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {allParticipants.map((participant) => (
                                <div key={`${participant.slot_info.id}-${participant.id}`} className="bg-gray-50 p-4 rounded-lg border">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {/* Participant Info */}
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <h4 className="font-semibold text-gray-900">{participant.name}</h4>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(participant.payment_status)}`}>
                                                    {participant.payment_status.charAt(0).toUpperCase() + participant.payment_status.slice(1)}
                                                </span>
                                            </div>
                                            
                                            <div className="space-y-1 text-sm">
                                                <p className="text-gray-600">
                                                    📞 {participant.contact}
                                                </p>
                                                <p className="text-gray-600">
                                                    👥 {participant.total_persons} person(s) • ₹{participant.total_amount.toLocaleString()}
                                                </p>
                                                <div className="flex gap-3 text-xs">
                                                    {participant.ticket_id && (
                                                        <span className="text-purple-600">🎫 {participant.ticket_id}</span>
                                                    )}
                                                    {participant.boat_name && (
                                                        <span className="text-blue-600">🚢 {participant.boat_name}</span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Slot Info */}
                                        <div>
                                            <h5 className="font-medium text-gray-700 mb-2">Slot Details</h5>
                                            <div className="space-y-1 text-sm">
                                                <p className="text-gray-600">
                                                    <span className={getSlotStatusColor(participant.slot_info.status)}>
                                                        {participant.slot_info.activity_name}
                                                    </span>
                                                </p>
                                                <p className="text-gray-600">
                                                    📅 {participant.slot_info.date} at {participant.slot_info.time}
                                                </p>
                                                <p className="text-gray-600">
                                                    Status: <span className={getSlotStatusColor(participant.slot_info.status)}>
                                                        {participant.slot_info.status.charAt(0).toUpperCase() + participant.slot_info.status.slice(1)}
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div className="mt-6 flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                        Total Revenue: ₹{allParticipants.reduce((sum, p) => sum + p.total_amount, 0).toLocaleString()}
                    </div>
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ParticipantsListModal;
