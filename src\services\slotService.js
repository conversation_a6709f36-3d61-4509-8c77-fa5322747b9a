import paymentService from './paymentService';

class SlotService {
    constructor() {
        this.storageKey = 'activity_crm_slots';
        this.initializeData();
    }

    initializeData() {
        const existingData = localStorage.getItem(this.storageKey);
        if (!existingData) {
            const initialData = {
                slots: [],
                next_slot_id: 1
            };
            localStorage.setItem(this.storageKey, JSON.stringify(initialData));
        }
    }

    getSlotData() {
        const data = localStorage.getItem(this.storageKey);
        return data ? JSON.parse(data) : { slots: [], next_slot_id: 1 };
    }

    updateSlotData(data) {
        localStorage.setItem(this.storageKey, JSON.stringify(data));
    }

    // Simulate API delay
    delay() {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    // Create a new slot
    async createSlot(slotData, currentUser) {
        await this.delay();
        const data = this.getSlotData();

        const newSlot = {
            id: data.next_slot_id,
            activity_id: slotData.activity_id,
            activity_name: slotData.activity_name,
            date: slotData.date,
            time: slotData.time,
            capacity: 12, // Semi-submarine capacity
            booked_count: 0,
            available_count: 12,
            status: 'active', // active, cancelled, completed
            participants: [],
            total_amount: 0,
            collected_amount: 0,
            commission_amount: 0,
            created_by: currentUser.id,
            created_by_name: currentUser.name,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        data.slots.push(newSlot);
        data.next_slot_id += 1;
        this.updateSlotData(data);

        return newSlot;
    }

    // Get all slots
    async getSlots(filters = {}) {
        await this.delay();
        const data = this.getSlotData();
        let slots = [...data.slots];

        // Apply filters
        if (filters.date) {
            slots = slots.filter(slot => slot.date === filters.date);
        }
        if (filters.status) {
            slots = slots.filter(slot => slot.status === filters.status);
        }
        if (filters.activity_id) {
            slots = slots.filter(slot => slot.activity_id === parseInt(filters.activity_id));
        }

        // Sort by date and time
        slots.sort((a, b) => {
            const dateA = new Date(`${a.date} ${a.time}`);
            const dateB = new Date(`${b.date} ${b.time}`);
            return dateA - dateB;
        });

        return slots;
    }

    // Get slot by ID
    async getSlotById(slotId) {
        await this.delay();
        const data = this.getSlotData();
        return data.slots.find(slot => slot.id === parseInt(slotId));
    }

    // Add participant to slot
    async addParticipant(slotId, participantData, currentUser) {
        await this.delay();
        const data = this.getSlotData();
        const slotIndex = data.slots.findIndex(slot => slot.id === parseInt(slotId));

        if (slotIndex === -1) {
            throw new Error('Slot not found');
        }

        const slot = data.slots[slotIndex];

        if (slot.booked_count >= slot.capacity) {
            throw new Error('Slot is full');
        }

        if (slot.status !== 'active') {
            throw new Error('Slot is not active');
        }

        const participant = {
            id: Date.now(),
            ticket_id: participantData.ticket_id || null,
            boat_name: participantData.boat_name,
            name: participantData.name,
            contact: participantData.contact,
            adults: participantData.adults || 1,
            children: participantData.children || 0,
            total_persons: (participantData.adults || 1) + (participantData.children || 0),
            amount_per_person: participantData.amount_per_person || 3500,
            total_amount: ((participantData.adults || 1) + (participantData.children || 0)) * (participantData.amount_per_person || 3500),
            payment_status: 'pending', // pending, collected, refunded
            added_by: currentUser.id,
            added_by_name: currentUser.name,
            added_at: new Date().toISOString()
        };

        // Check if adding this participant would exceed capacity
        if (slot.booked_count + participant.total_persons > slot.capacity) {
            throw new Error(`Cannot add ${participant.total_persons} persons. Only ${slot.available_count} spots available.`);
        }

        slot.participants.push(participant);
        slot.booked_count += participant.total_persons;
        slot.available_count = slot.capacity - slot.booked_count;
        slot.total_amount += participant.total_amount;
        slot.updated_at = new Date().toISOString();

        data.slots[slotIndex] = slot;
        this.updateSlotData(data);

        return slot;
    }

    // Collect payment from participant
    async collectPayment(slotId, participantId, currentUser) {
        await this.delay();
        const data = this.getSlotData();
        const slotIndex = data.slots.findIndex(slot => slot.id === parseInt(slotId));

        if (slotIndex === -1) {
            throw new Error('Slot not found');
        }

        const slot = data.slots[slotIndex];
        const participantIndex = slot.participants.findIndex(p => p.id === parseInt(participantId));

        if (participantIndex === -1) {
            throw new Error('Participant not found');
        }

        const participant = slot.participants[participantIndex];

        if (participant.payment_status === 'collected') {
            throw new Error('Payment already collected');
        }

        participant.payment_status = 'collected';
        participant.collected_by = currentUser.id;
        participant.collected_by_name = currentUser.name;
        participant.collected_at = new Date().toISOString();

        slot.collected_amount += participant.total_amount;

        // Fixed commission for Elephanta staff per booking
        const commissionAmount = currentUser.commission_amount || 350; // ₹350 default per booking
        slot.commission_amount += commissionAmount;

        slot.updated_at = new Date().toISOString();

        data.slots[slotIndex] = slot;
        this.updateSlotData(data);

        // Record payment collection
        try {
            await paymentService.processPaymentCollection(
                slotId,
                participantId,
                participant.total_amount,
                currentUser
            );
        } catch (error) {
            console.error('Failed to record payment:', error);
        }

        return slot;
    }

    // Cancel slot (weather or other conditions)
    async cancelSlot(slotId, reason, currentUser) {
        await this.delay();
        const data = this.getSlotData();
        const slotIndex = data.slots.findIndex(slot => slot.id === parseInt(slotId));

        if (slotIndex === -1) {
            throw new Error('Slot not found');
        }

        const slot = data.slots[slotIndex];

        if (slot.status !== 'active') {
            throw new Error('Only active slots can be cancelled');
        }

        // Mark all collected payments as refunded and process refunds
        for (const participant of slot.participants) {
            if (participant.payment_status === 'collected') {
                participant.payment_status = 'refunded';
                participant.refunded_by = currentUser.id;
                participant.refunded_by_name = currentUser.name;
                participant.refunded_at = new Date().toISOString();
                participant.refund_reason = reason;

                // Process refund through payment service
                try {
                    await paymentService.processRefund(
                        slotId,
                        participant.id,
                        participant.total_amount,
                        currentUser,
                        reason
                    );
                } catch (error) {
                    console.error('Failed to process refund:', error);
                }
            }
        }

        // Mark slot as cancelled
        slot.status = 'cancelled';
        slot.cancellation_reason = reason;
        slot.cancelled_by = currentUser.id;
        slot.cancelled_by_name = currentUser.name;
        slot.cancelled_at = new Date().toISOString();
        slot.updated_at = new Date().toISOString();

        data.slots[slotIndex] = slot;
        this.updateSlotData(data);

        return slot;
    }

    // Permanently delete cancelled slot
    async deleteSlot(slotId, currentUser) {
        await this.delay();
        const data = this.getSlotData();
        const slotIndex = data.slots.findIndex(slot => slot.id === parseInt(slotId));

        if (slotIndex === -1) {
            throw new Error('Slot not found');
        }

        const slot = data.slots[slotIndex];

        if (slot.status !== 'cancelled') {
            throw new Error('Only cancelled slots can be deleted');
        }

        // Remove the slot completely from the array
        data.slots.splice(slotIndex, 1);
        this.updateSlotData(data);

        return { message: 'Slot deleted permanently' };
    }

    // Refund individual participant
    async refundParticipant(slotId, participantId, reason, currentUser) {
        await this.delay();
        const data = this.getSlotData();
        const slotIndex = data.slots.findIndex(slot => slot.id === parseInt(slotId));

        if (slotIndex === -1) {
            throw new Error('Slot not found');
        }

        const slot = data.slots[slotIndex];
        const participantIndex = slot.participants.findIndex(p => p.id === parseInt(participantId));

        if (participantIndex === -1) {
            throw new Error('Participant not found');
        }

        const participant = slot.participants[participantIndex];

        if (participant.payment_status !== 'collected') {
            throw new Error('Can only refund collected payments');
        }

        if (participant.payment_status === 'refunded') {
            throw new Error('Participant already refunded');
        }

        // Update participant status
        participant.payment_status = 'refunded';
        participant.refunded_by = currentUser.id;
        participant.refunded_by_name = currentUser.name;
        participant.refunded_at = new Date().toISOString();
        participant.refund_reason = reason;

        // Update slot totals
        slot.booked_count -= participant.total_persons;
        slot.available_count = slot.capacity - slot.booked_count;
        slot.collected_amount -= participant.total_amount;
        slot.commission_amount -= (currentUser.commission_amount || 350);
        slot.updated_at = new Date().toISOString();

        data.slots[slotIndex] = slot;
        this.updateSlotData(data);

        // Process refund through payment service
        try {
            await paymentService.processRefund(
                slotId,
                participantId,
                participant.total_amount,
                currentUser,
                reason
            );
        } catch (error) {
            console.error('Failed to process refund in payment service:', error);
        }

        return slot;
    }

    // Find available slot for ticket or create new one
    async findAvailableSlotForTicket(ticket, currentUser) {
        await this.delay();
        const data = this.getSlotData();

        // Look for existing active slot on the ticket date with enough capacity
        let slot = data.slots.find(s =>
            s.date === ticket.booking_date &&
            s.status === 'active' &&
            s.available_count >= ticket.total_persons
        );

        // If no slot with enough capacity, create a new one
        if (!slot) {
            const newSlot = {
                id: data.next_slot_id,
                activity_id: 1, // Default activity
                activity_name: 'Elephanta Caves Tour',
                date: ticket.booking_date,
                time: '10:00', // Default time - staff can change this
                capacity: 12,
                booked_count: 0,
                available_count: 12,
                status: 'active',
                participants: [],
                total_amount: 0,
                collected_amount: 0,
                commission_amount: 0,
                created_by: currentUser.id,
                created_by_name: currentUser.name,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                auto_created: true // Mark as auto-created from ticket
            };

            data.slots.push(newSlot);
            data.next_slot_id += 1;
            this.updateSlotData(data);

            slot = newSlot;
        }

        return slot;
    }

    // Add participant by ticket ID (auto-find/create slot)
    async addParticipantByTicket(ticketId, boatName, currentUser) {
        await this.delay();

        // Import ticket service here to avoid circular dependency
        const ticketService = (await import('./ticketService')).default;

        // Get and validate ticket
        const ticket = await ticketService.validateTicketForSlot(ticketId);

        // Find available slot or create new one
        const slot = await this.findAvailableSlotForTicket(ticket, currentUser);

        // Create participant data from ticket
        const participantData = {
            ticket_id: ticketId,
            boat_name: boatName,
            name: ticket.customer_name,
            contact: ticket.contact,
            email: ticket.email || '',
            adults: ticket.adults,
            children: ticket.children,
            amount_per_person: 3500
        };

        // Add participant to the slot
        const updatedSlot = await this.addParticipant(slot.id, participantData, currentUser);

        // Mark ticket as used
        await ticketService.markTicketAsUsed(ticketId, slot.id);

        return {
            slot: updatedSlot,
            ticket: ticket,
            isNewSlot: slot.auto_created && updatedSlot.participants.length === 1,
            message: slot.auto_created && updatedSlot.participants.length === 1
                ? `New slot created for ${ticket.booking_date} with ${ticket.total_persons} participants`
                : `Added ${ticket.total_persons} participants to existing slot (${updatedSlot.booked_count}/${updatedSlot.capacity})`
        };
    }

    // Get today's slots
    async getTodaysSlots() {
        const today = new Date().toISOString().split('T')[0];
        return this.getSlots({ date: today });
    }

    // Transfer collected money to admin
    async transferToAdmin(currentUser, transferMode = 'cash') {
        try {
            const pendingPayments = await paymentService.getPendingPayments(currentUser.id);

            if (pendingPayments.length === 0) {
                throw new Error('No pending payments to transfer');
            }

            const paymentIds = pendingPayments.map(p => p.id);
            const transfer = await paymentService.transferToAdmin(paymentIds, currentUser, transferMode);

            return transfer;
        } catch (error) {
            throw error;
        }
    }

    // Get payment summary for current user
    async getPaymentSummary(currentUser) {
        try {
            return await paymentService.getElephantaStaffSummary(currentUser.id);
        } catch (error) {
            throw error;
        }
    }

    // Get slot statistics
    async getSlotStats() {
        await this.delay();
        const data = this.getSlotData();
        const slots = data.slots;

        const stats = {
            total_slots: slots.length,
            active_slots: slots.filter(s => s.status === 'active').length,
            cancelled_slots: slots.filter(s => s.status === 'cancelled').length,
            completed_slots: slots.filter(s => s.status === 'completed').length,
            total_participants: slots.reduce((sum, slot) => sum + slot.booked_count, 0),
            total_revenue: slots.reduce((sum, slot) => sum + slot.collected_amount, 0),
            total_commission: slots.reduce((sum, slot) => sum + slot.commission_amount, 0),
            pending_amount: slots.reduce((sum, slot) => sum + (slot.total_amount - slot.collected_amount), 0)
        };

        return stats;
    }

    // Passenger Management Methods
    async getPassengersByStatus(date) {
        await this.delay();
        const data = this.getSlotData();
        const slots = data.slots.filter(s => s.date === date);

        const arrival = [];
        const departure = [];
        const completed = [];

        slots.forEach(slot => {
            slot.participants.forEach(participant => {
                const passengerData = {
                    id: participant.id,
                    name: participant.name,
                    phone: participant.contact,
                    email: participant.email,
                    slot_time: slot.time,
                    activity_name: slot.activity_name,
                    group_size: participant.adults + participant.children,
                    status: participant.status,
                    arrival_time: participant.arrival_time,
                    departure_time: participant.departure_time,
                    completed_time: participant.completed_time
                };

                if (participant.status === 'confirmed' && !participant.arrival_time) {
                    arrival.push(passengerData);
                } else if (participant.arrival_time && !participant.departure_time) {
                    departure.push(passengerData);
                } else if (participant.completed_time) {
                    completed.push(passengerData);
                }
            });
        });

        return { arrival, departure, completed };
    }

    async markPassengerArrival(passengerId) {
        await this.delay();
        const data = this.getSlotData();

        for (let slot of data.slots) {
            const participant = slot.participants.find(p => p.id === passengerId);
            if (participant) {
                participant.arrival_time = new Date().toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });
                participant.status = 'arrived';
                this.updateSlotData(data);
                return { success: true, message: 'Passenger arrival marked successfully' };
            }
        }

        throw new Error('Passenger not found');
    }

    async markPassengerDeparture(passengerId) {
        await this.delay();
        const data = this.getSlotData();

        for (let slot of data.slots) {
            const participant = slot.participants.find(p => p.id === passengerId);
            if (participant) {
                participant.departure_time = new Date().toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });
                participant.status = 'departed';
                this.updateSlotData(data);
                return { success: true, message: 'Passenger departure marked successfully' };
            }
        }

        throw new Error('Passenger not found');
    }

    async markTripCompleted(passengerId) {
        await this.delay();
        const data = this.getSlotData();

        for (let slot of data.slots) {
            const participant = slot.participants.find(p => p.id === passengerId);
            if (participant) {
                participant.completed_time = new Date().toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });
                participant.status = 'completed';
                this.updateSlotData(data);
                return { success: true, message: 'Trip marked as completed successfully' };
            }
        }

        throw new Error('Passenger not found');
    }
}

const slotService = new SlotService();
export default slotService;
