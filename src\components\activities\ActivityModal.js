import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const ActivityModal = ({ isOpen, onClose, onSave, activity = null, mode = 'create' }) => {
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        price: '',
        child_price: '',
        status: 'active',
        gst_rate: 18,
        commission_structure: {
            agent_commission: '',
            staff_commission: '',
            boat_boy_commission: ''
        },
        child_commission_structure: {
            agent_commission: '',
            staff_commission: '',
            boat_boy_commission: ''
        }
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (activity && mode === 'edit') {
            setFormData({
                name: activity.name,
                description: activity.description,
                price: activity.price.toString(),
                child_price: activity.child_price ? activity.child_price.toString() : '',
                status: activity.status,
                gst_rate: activity.gst_rate,
                commission_structure: {
                    agent_commission: activity.commission_structure.agent_commission.toString(),
                    staff_commission: activity.commission_structure.staff_commission.toString(),
                    boat_boy_commission: activity.commission_structure.boat_boy_commission.toString()
                },
                child_commission_structure: activity.child_commission_structure ? {
                    agent_commission: activity.child_commission_structure.agent_commission.toString(),
                    staff_commission: activity.child_commission_structure.staff_commission.toString(),
                    boat_boy_commission: activity.child_commission_structure.boat_boy_commission.toString()
                } : {
                    agent_commission: '',
                    staff_commission: '',
                    boat_boy_commission: ''
                }
            });
        } else {
            // Reset form for create mode
            setFormData({
                name: '',
                description: '',
                price: '',
                child_price: '',
                status: 'active',
                gst_rate: 18,
                commission_structure: {
                    agent_commission: '',
                    staff_commission: '',
                    boat_boy_commission: ''
                },
                child_commission_structure: {
                    agent_commission: '',
                    staff_commission: '',
                    boat_boy_commission: ''
                }
            });
        }
        setErrors({});
    }, [activity, mode, isOpen]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Activity name is required';
        }

        if (!formData.description.trim()) {
            newErrors.description = 'Description is required';
        }

        const price = parseFloat(formData.price);
        if (!formData.price || isNaN(price) || price <= 0) {
            newErrors.price = 'Valid adult price is required';
        }

        // Validate child price if provided
        if (formData.child_price) {
            const childPrice = parseFloat(formData.child_price);
            if (isNaN(childPrice) || childPrice < 0) {
                newErrors.child_price = 'Valid child price is required';
            }
        }

        // Validate adult commission structure
        const agentCommission = parseFloat(formData.commission_structure.agent_commission) || 0;
        const staffCommission = parseFloat(formData.commission_structure.staff_commission) || 0;
        const boatBoyCommission = parseFloat(formData.commission_structure.boat_boy_commission) || 0;

        if (agentCommission < 0) {
            newErrors.agent_commission = 'Agent commission cannot be negative';
        }
        if (staffCommission < 0) {
            newErrors.staff_commission = 'Staff commission cannot be negative';
        }
        if (boatBoyCommission < 0) {
            newErrors.boat_boy_commission = 'Boat boy commission cannot be negative';
        }

        const totalCommission = agentCommission + staffCommission + boatBoyCommission;
        if (totalCommission > price) {
            newErrors.total_commission = 'Total adult commission cannot exceed adult activity price';
        }

        // Validate child commission structure if child price is provided
        if (formData.child_price) {
            const childPrice = parseFloat(formData.child_price) || 0;
            const childAgentCommission = parseFloat(formData.child_commission_structure.agent_commission) || 0;
            const childStaffCommission = parseFloat(formData.child_commission_structure.staff_commission) || 0;
            const childBoatBoyCommission = parseFloat(formData.child_commission_structure.boat_boy_commission) || 0;

            if (childAgentCommission < 0) {
                newErrors.child_agent_commission = 'Child agent commission cannot be negative';
            }
            if (childStaffCommission < 0) {
                newErrors.child_staff_commission = 'Child staff commission cannot be negative';
            }
            if (childBoatBoyCommission < 0) {
                newErrors.child_boat_boy_commission = 'Child boat boy commission cannot be negative';
            }

            const childTotalCommission = childAgentCommission + childStaffCommission + childBoatBoyCommission;
            if (childTotalCommission > childPrice) {
                newErrors.child_total_commission = 'Total child commission cannot exceed child activity price';
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            const submitData = {
                ...formData,
                price: parseFloat(formData.price),
                child_price: formData.child_price ? parseFloat(formData.child_price) : null,
                commission_structure: {
                    agent_commission: parseFloat(formData.commission_structure.agent_commission) || 0,
                    staff_commission: parseFloat(formData.commission_structure.staff_commission) || 0,
                    boat_boy_commission: parseFloat(formData.commission_structure.boat_boy_commission) || 0
                },
                child_commission_structure: formData.child_price ? {
                    agent_commission: parseFloat(formData.child_commission_structure.agent_commission) || 0,
                    staff_commission: parseFloat(formData.child_commission_structure.staff_commission) || 0,
                    boat_boy_commission: parseFloat(formData.child_commission_structure.boat_boy_commission) || 0
                } : null
            };

            await onSave(submitData);
            toast.success(`Activity ${mode === 'create' ? 'created' : 'updated'} successfully!`);
            onClose();
        } catch (error) {
            toast.error(error.message || `Failed to ${mode} activity`);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (field, value) => {
        if (field.includes('.')) {
            const [parent, child] = field.split('.');
            setFormData(prev => ({
                ...prev,
                [parent]: {
                    ...prev[parent],
                    [child]: value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [field]: value
            }));
        }
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }));
        }
    };

    const calculateTotals = () => {
        const price = parseFloat(formData.price) || 0;
        const agentCommission = parseFloat(formData.commission_structure.agent_commission) || 0;
        const staffCommission = parseFloat(formData.commission_structure.staff_commission) || 0;
        const boatBoyCommission = parseFloat(formData.commission_structure.boat_boy_commission) || 0;
        
        const totalCommission = agentCommission + staffCommission + boatBoyCommission;
        const remainingAmount = price - totalCommission;
        const gstAmount = (remainingAmount * formData.gst_rate) / 100;
        const adminShare = remainingAmount - gstAmount;

        return {
            totalCommission,
            remainingAmount,
            gstAmount,
            adminShare
        };
    };

    const totals = calculateTotals();

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        {mode === 'create' ? 'Add New Activity' : 'Edit Activity'}
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Activity Name *
                            </label>
                            <input
                                type="text"
                                value={formData.name}
                                onChange={(e) => handleInputChange('name', e.target.value)}
                                className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                    errors.name ? 'border-red-300' : 'border-gray-300'
                                } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                placeholder="Enter activity name"
                            />
                            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Description *
                            </label>
                            <textarea
                                value={formData.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                rows={3}
                                className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                    errors.description ? 'border-red-300' : 'border-gray-300'
                                } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                placeholder="Enter activity description"
                            />
                            {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                        </div>

                        <div className="grid grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Adult Price (₹) *
                                </label>
                                <input
                                    type="number"
                                    value={formData.price}
                                    onChange={(e) => handleInputChange('price', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.price ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Child Price (₹)
                                </label>
                                <input
                                    type="number"
                                    value={formData.child_price}
                                    onChange={(e) => handleInputChange('child_price', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.child_price ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.child_price && <p className="mt-1 text-sm text-red-600">{errors.child_price}</p>}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Status
                                </label>
                                <select
                                    value={formData.status}
                                    onChange={(e) => handleInputChange('status', e.target.value)}
                                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                >
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Adult Commission Structure */}
                    <div>
                        <h4 className="text-md font-medium text-gray-900 mb-4">Adult Commission Structure</h4>
                        <div className="grid grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Agent Commission (₹)
                                </label>
                                <input
                                    type="number"
                                    value={formData.commission_structure.agent_commission}
                                    onChange={(e) => handleInputChange('commission_structure.agent_commission', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.agent_commission ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.agent_commission && <p className="mt-1 text-sm text-red-600">{errors.agent_commission}</p>}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Staff Commission (₹)
                                </label>
                                <input
                                    type="number"
                                    value={formData.commission_structure.staff_commission}
                                    onChange={(e) => handleInputChange('commission_structure.staff_commission', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.staff_commission ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.staff_commission && <p className="mt-1 text-sm text-red-600">{errors.staff_commission}</p>}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Boat Boy Commission (₹)
                                </label>
                                <input
                                    type="number"
                                    value={formData.commission_structure.boat_boy_commission}
                                    onChange={(e) => handleInputChange('commission_structure.boat_boy_commission', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.boat_boy_commission ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.boat_boy_commission && <p className="mt-1 text-sm text-red-600">{errors.boat_boy_commission}</p>}
                            </div>
                        </div>
                        {errors.total_commission && (
                            <p className="mt-2 text-sm text-red-600">{errors.total_commission}</p>
                        )}
                    </div>

                    {/* Child Commission Structure */}
                    <div>
                        <h4 className="text-md font-medium text-gray-900 mb-4">Child Commission Structure</h4>
                        <div className="grid grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Agent Commission (₹)
                                </label>
                                <input
                                    type="number"
                                    value={formData.child_commission_structure.agent_commission}
                                    onChange={(e) => handleInputChange('child_commission_structure.agent_commission', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.child_agent_commission ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.child_agent_commission && <p className="mt-1 text-sm text-red-600">{errors.child_agent_commission}</p>}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Staff Commission (₹)
                                </label>
                                <input
                                    type="number"
                                    value={formData.child_commission_structure.staff_commission}
                                    onChange={(e) => handleInputChange('child_commission_structure.staff_commission', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.child_staff_commission ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.child_staff_commission && <p className="mt-1 text-sm text-red-600">{errors.child_staff_commission}</p>}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Boat Boy Commission (₹)
                                </label>
                                <input
                                    type="number"
                                    value={formData.child_commission_structure.boat_boy_commission}
                                    onChange={(e) => handleInputChange('child_commission_structure.boat_boy_commission', e.target.value)}
                                    className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                        errors.child_boat_boy_commission ? 'border-red-300' : 'border-gray-300'
                                    } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                    placeholder="0"
                                    min="0"
                                    step="0.01"
                                />
                                {errors.child_boat_boy_commission && <p className="mt-1 text-sm text-red-600">{errors.child_boat_boy_commission}</p>}
                            </div>
                        </div>
                        {errors.child_total_commission && (
                            <p className="mt-2 text-sm text-red-600">{errors.child_total_commission}</p>
                        )}
                    </div>

                    {/* Calculation Summary */}
                    {formData.price && (
                        <div className="bg-gray-50 p-4 rounded-md">
                            <h5 className="text-sm font-medium text-gray-900 mb-2">Calculation Summary</h5>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-gray-600">Total Commission:</span>
                                    <span className="float-right font-medium">₹{totals.totalCommission.toFixed(2)}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Remaining Amount:</span>
                                    <span className="float-right font-medium">₹{totals.remainingAmount.toFixed(2)}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">GST ({formData.gst_rate}%):</span>
                                    <span className="float-right font-medium">₹{totals.gstAmount.toFixed(2)}</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">Admin Share:</span>
                                    <span className="float-right font-medium">₹{totals.adminShare.toFixed(2)}</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Form Actions */}
                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
                        >
                            {loading ? 'Saving...' : (mode === 'create' ? 'Create Activity' : 'Update Activity')}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default ActivityModal;
