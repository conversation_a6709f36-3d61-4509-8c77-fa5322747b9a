// Personnel Management Service
// Handles CRUD operations for staff and boat boys

class PersonnelService {
    constructor() {
        this.initializeSampleData();
    }

    // Initialize sample data in localStorage
    initializeSampleData() {
        if (!localStorage.getItem('activity_crm_personnel')) {
            const sampleData = {
                staff: [
                    {
                        id: 1,
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        phone: '+91 9876543210',
                        employee_id: 'EMP001',
                        status: 'active',
                        hire_date: '2024-01-15',
                        wallet_balance: 2500.00,
                        total_earned: 12000.00,
                        total_paid: 9500.00,
                        last_activity: '2025-01-06',
                        created_at: '2024-01-15',
                        updated_at: '2025-01-06'
                    },
                    {
                        id: 2,
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        phone: '+91 9876543211',
                        employee_id: 'EMP002',
                        status: 'active',
                        hire_date: '2024-02-01',
                        wallet_balance: 1800.00,
                        total_earned: 8500.00,
                        total_paid: 6700.00,
                        last_activity: '2025-01-05',
                        created_at: '2024-02-01',
                        updated_at: '2025-01-05'
                    },
                    {
                        id: 3,
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        phone: '+91 9876543212',
                        employee_id: 'EMP003',
                        status: 'inactive',
                        hire_date: '2024-03-10',
                        wallet_balance: 500.00,
                        total_earned: 3200.00,
                        total_paid: 2700.00,
                        last_activity: '2024-12-20',
                        created_at: '2024-03-10',
                        updated_at: '2024-12-20'
                    }
                ],
                boat_boys: [
                    {
                        id: 1,
                        name: 'Ravi Kumar',
                        phone: '+91 **********',
                        boat_license: 'BL001',
                        status: 'active',
                        hire_date: '2024-01-20',
                        wallet_balance: 1200.00,
                        total_earned: 6500.00,
                        total_paid: 5300.00,
                        last_activity: '2025-01-06',
                        created_at: '2024-01-20',
                        updated_at: '2025-01-06'
                    },
                    {
                        id: 2,
                        name: 'Suresh Patel',
                        phone: '+91 **********',
                        boat_license: 'BL002',
                        status: 'active',
                        hire_date: '2024-02-15',
                        wallet_balance: 900.00,
                        total_earned: 4800.00,
                        total_paid: 3900.00,
                        last_activity: '2025-01-05',
                        created_at: '2024-02-15',
                        updated_at: '2025-01-05'
                    },
                    {
                        id: 3,
                        name: 'Amit Singh',
                        phone: '+91 **********',
                        boat_license: 'BL003',
                        status: 'active',
                        hire_date: '2024-03-01',
                        wallet_balance: 750.00,
                        total_earned: 3200.00,
                        total_paid: 2450.00,
                        last_activity: '2025-01-04',
                        created_at: '2024-03-01',
                        updated_at: '2025-01-04'
                    }
                ],
                agents: [
                    {
                        id: 1,
                        name: 'Sarah Wilson',
                        email: '<EMAIL>',
                        phone: '+91 **********',
                        agent_code: 'AGT001',
                        status: 'active',
                        hire_date: '2024-01-10',
                        wallet_balance: 3200.00,
                        total_earned: 18500.00,
                        total_paid: 15300.00,
                        last_activity: '2025-01-06',
                        created_at: '2024-01-10',
                        updated_at: '2025-01-06'
                    },
                    {
                        id: 2,
                        name: 'David Brown',
                        email: '<EMAIL>',
                        phone: '+91 9876543231',
                        agent_code: 'AGT002',
                        status: 'active',
                        hire_date: '2024-02-20',
                        wallet_balance: 2800.00,
                        total_earned: 14200.00,
                        total_paid: 11400.00,
                        last_activity: '2025-01-05',
                        created_at: '2024-02-20',
                        updated_at: '2025-01-05'
                    },
                    {
                        id: 3,
                        name: 'Lisa Garcia',
                        email: '<EMAIL>',
                        phone: '+91 9876543232',
                        agent_code: 'AGT003',
                        status: 'inactive',
                        hire_date: '2024-04-15',
                        wallet_balance: 800.00,
                        total_earned: 5600.00,
                        total_paid: 4800.00,
                        last_activity: '2024-12-15',
                        created_at: '2024-04-15',
                        updated_at: '2024-12-15'
                    },
                    {
                        id: 4,
                        name: 'Michael Johnson',
                        email: '<EMAIL>',
                        phone: '+91 9876543233',
                        agent_code: 'AGT004',
                        status: 'active',
                        hire_date: '2024-03-15',
                        wallet_balance: 4500.00,
                        total_earned: 22000.00,
                        total_paid: 17500.00,
                        last_activity: '2025-01-07',
                        created_at: '2024-03-15',
                        updated_at: '2025-01-07'
                    },
                    {
                        id: 5,
                        name: 'Emily Davis',
                        email: '<EMAIL>',
                        phone: '+91 9876543234',
                        agent_code: 'AGT005',
                        status: 'active',
                        hire_date: '2024-05-20',
                        wallet_balance: 1900.00,
                        total_earned: 12800.00,
                        total_paid: 10900.00,
                        last_activity: '2025-01-06',
                        created_at: '2024-05-20',
                        updated_at: '2025-01-06'
                    },
                    {
                        id: 6,
                        name: 'Robert Martinez',
                        email: '<EMAIL>',
                        phone: '+91 9876543235',
                        agent_code: 'AGT006',
                        status: 'active',
                        hire_date: '2024-06-10',
                        wallet_balance: 3700.00,
                        total_earned: 16200.00,
                        total_paid: 12500.00,
                        last_activity: '2025-01-05',
                        created_at: '2024-06-10',
                        updated_at: '2025-01-05'
                    },
                    {
                        id: 7,
                        name: 'Jennifer Lee',
                        email: '<EMAIL>',
                        phone: '+91 9876543236',
                        agent_code: 'AGT007',
                        status: 'active',
                        hire_date: '2024-07-25',
                        wallet_balance: 2300.00,
                        total_earned: 9800.00,
                        total_paid: 7500.00,
                        last_activity: '2025-01-04',
                        created_at: '2024-07-25',
                        updated_at: '2025-01-04'
                    },
                    {
                        id: 8,
                        name: 'Christopher Taylor',
                        email: '<EMAIL>',
                        phone: '+91 9876543237',
                        agent_code: 'AGT008',
                        status: 'active',
                        hire_date: '2024-08-12',
                        wallet_balance: 1500.00,
                        total_earned: 7200.00,
                        total_paid: 5700.00,
                        last_activity: '2025-01-03',
                        created_at: '2024-08-12',
                        updated_at: '2025-01-03'
                    }
                ],
                next_staff_id: 4,
                next_boat_boy_id: 4,
                next_agent_id: 9
            };
            localStorage.setItem('activity_crm_personnel', JSON.stringify(sampleData));
        }
    }

    // Get all personnel data from localStorage
    getPersonnelData() {
        const data = localStorage.getItem('activity_crm_personnel');
        return data ? JSON.parse(data) : {
            staff: [],
            boat_boys: [],
            agents: [],
            next_staff_id: 1,
            next_boat_boy_id: 1,
            next_agent_id: 1
        };
    }

    // Update personnel data in localStorage
    updatePersonnelData(data) {
        localStorage.setItem('activity_crm_personnel', JSON.stringify(data));
    }

    // Simulate API delay
    delay(ms = 500) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // STAFF MANAGEMENT

    // Get all staff
    async getStaff(filters = {}) {
        await this.delay();
        const data = this.getPersonnelData();
        let staff = data.staff;

        // Apply filters
        if (filters.status) {
            staff = staff.filter(s => s.status === filters.status);
        }
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            staff = staff.filter(s => 
                s.name.toLowerCase().includes(searchTerm) ||
                s.email.toLowerCase().includes(searchTerm) ||
                s.employee_id.toLowerCase().includes(searchTerm)
            );
        }

        return {
            staff,
            total: staff.length,
            active_count: data.staff.filter(s => s.status === 'active').length,
            inactive_count: data.staff.filter(s => s.status === 'inactive').length,
            total_wallet_balance: data.staff.reduce((sum, s) => sum + s.wallet_balance, 0)
        };
    }

    // Get single staff member
    async getStaffMember(id) {
        await this.delay();
        const data = this.getPersonnelData();
        const staff = data.staff.find(s => s.id === parseInt(id));
        if (!staff) {
            throw new Error('Staff member not found');
        }
        return staff;
    }

    // Create new staff member
    async createStaff(staffData) {
        await this.delay();
        const data = this.getPersonnelData();
        
        // Check for duplicate email or employee_id
        const existingEmail = data.staff.find(s => s.email === staffData.email);
        if (existingEmail) {
            throw new Error('Email already exists');
        }
        
        const existingEmpId = data.staff.find(s => s.employee_id === staffData.employee_id);
        if (existingEmpId) {
            throw new Error('Employee ID already exists');
        }

        const newStaff = {
            id: data.next_staff_id,
            ...staffData,
            wallet_balance: 0,
            total_earned: 0,
            total_paid: 0,
            last_activity: null,
            created_at: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.staff.push(newStaff);
        data.next_staff_id += 1;
        this.updatePersonnelData(data);

        return newStaff;
    }

    // Update staff member
    async updateStaff(id, staffData) {
        await this.delay();
        const data = this.getPersonnelData();
        const staffIndex = data.staff.findIndex(s => s.id === parseInt(id));
        
        if (staffIndex === -1) {
            throw new Error('Staff member not found');
        }

        // Check for duplicate email or employee_id (excluding current record)
        if (staffData.email) {
            const existingEmail = data.staff.find(s => s.email === staffData.email && s.id !== parseInt(id));
            if (existingEmail) {
                throw new Error('Email already exists');
            }
        }
        
        if (staffData.employee_id) {
            const existingEmpId = data.staff.find(s => s.employee_id === staffData.employee_id && s.id !== parseInt(id));
            if (existingEmpId) {
                throw new Error('Employee ID already exists');
            }
        }

        const updatedStaff = {
            ...data.staff[staffIndex],
            ...staffData,
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.staff[staffIndex] = updatedStaff;
        this.updatePersonnelData(data);

        return updatedStaff;
    }

    // Delete staff member
    async deleteStaff(id) {
        await this.delay();
        const data = this.getPersonnelData();
        const staffIndex = data.staff.findIndex(s => s.id === parseInt(id));
        
        if (staffIndex === -1) {
            throw new Error('Staff member not found');
        }

        data.staff.splice(staffIndex, 1);
        this.updatePersonnelData(data);

        return { success: true, message: 'Staff member deleted successfully' };
    }

    // BOAT BOY MANAGEMENT

    // Get all boat boys
    async getBoatBoys(filters = {}) {
        await this.delay();
        const data = this.getPersonnelData();
        let boatBoys = data.boat_boys;

        // Apply filters
        if (filters.status) {
            boatBoys = boatBoys.filter(b => b.status === filters.status);
        }
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            boatBoys = boatBoys.filter(b => 
                b.name.toLowerCase().includes(searchTerm) ||
                b.boat_license.toLowerCase().includes(searchTerm)
            );
        }

        return {
            boat_boys: boatBoys,
            total: boatBoys.length,
            active_count: data.boat_boys.filter(b => b.status === 'active').length,
            inactive_count: data.boat_boys.filter(b => b.status === 'inactive').length,
            total_wallet_balance: data.boat_boys.reduce((sum, b) => sum + b.wallet_balance, 0)
        };
    }

    // Create new boat boy
    async createBoatBoy(boatBoyData) {
        await this.delay();
        const data = this.getPersonnelData();
        
        // Check for duplicate boat license
        const existingLicense = data.boat_boys.find(b => b.boat_license === boatBoyData.boat_license);
        if (existingLicense) {
            throw new Error('Boat license already exists');
        }

        const newBoatBoy = {
            id: data.next_boat_boy_id,
            ...boatBoyData,
            wallet_balance: 0,
            total_earned: 0,
            total_paid: 0,
            last_activity: null,
            created_at: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.boat_boys.push(newBoatBoy);
        data.next_boat_boy_id += 1;
        this.updatePersonnelData(data);

        return newBoatBoy;
    }

    // Update boat boy
    async updateBoatBoy(id, boatBoyData) {
        await this.delay();
        const data = this.getPersonnelData();
        const boatBoyIndex = data.boat_boys.findIndex(b => b.id === parseInt(id));
        
        if (boatBoyIndex === -1) {
            throw new Error('Boat boy not found');
        }

        // Check for duplicate boat license (excluding current record)
        if (boatBoyData.boat_license) {
            const existingLicense = data.boat_boys.find(b => b.boat_license === boatBoyData.boat_license && b.id !== parseInt(id));
            if (existingLicense) {
                throw new Error('Boat license already exists');
            }
        }

        const updatedBoatBoy = {
            ...data.boat_boys[boatBoyIndex],
            ...boatBoyData,
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.boat_boys[boatBoyIndex] = updatedBoatBoy;
        this.updatePersonnelData(data);

        return updatedBoatBoy;
    }

    // Delete boat boy
    async deleteBoatBoy(id) {
        await this.delay();
        const data = this.getPersonnelData();
        const boatBoyIndex = data.boat_boys.findIndex(b => b.id === parseInt(id));
        
        if (boatBoyIndex === -1) {
            throw new Error('Boat boy not found');
        }

        data.boat_boys.splice(boatBoyIndex, 1);
        this.updatePersonnelData(data);

        return { success: true, message: 'Boat boy deleted successfully' };
    }

    // Get active staff for dropdowns
    async getActiveStaff() {
        await this.delay();
        const data = this.getPersonnelData();
        return data.staff
            .filter(staff => staff.status === 'active')
            .map(staff => ({
                id: staff.id,
                name: staff.name,
                employee_id: staff.employee_id
            }));
    }

    // Get active boat boys for dropdowns
    async getActiveBoatBoys() {
        await this.delay();
        const data = this.getPersonnelData();
        return data.boat_boys
            .filter(boatBoy => boatBoy.status === 'active')
            .map(boatBoy => ({
                id: boatBoy.id,
                name: boatBoy.name,
                boat_license: boatBoy.boat_license
            }));
    }

    // AGENT MANAGEMENT

    // Get all agents
    async getAgents(filters = {}) {
        await this.delay();
        const data = this.getPersonnelData();
        let agents = data.agents || [];

        // Apply filters
        if (filters.status) {
            agents = agents.filter(a => a.status === filters.status);
        }
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            agents = agents.filter(a =>
                a.name.toLowerCase().includes(searchTerm) ||
                a.email.toLowerCase().includes(searchTerm) ||
                a.agent_code.toLowerCase().includes(searchTerm)
            );
        }

        return {
            agents,
            total: agents.length,
            active_count: (data.agents || []).filter(a => a.status === 'active').length,
            inactive_count: (data.agents || []).filter(a => a.status === 'inactive').length,
            total_wallet_balance: (data.agents || []).reduce((sum, a) => sum + a.wallet_balance, 0)
        };
    }

    // Get single agent
    async getAgent(id) {
        await this.delay();
        const data = this.getPersonnelData();
        const agent = (data.agents || []).find(a => a.id === parseInt(id));
        if (!agent) {
            throw new Error('Agent not found');
        }
        return agent;
    }

    // Create new agent
    async createAgent(agentData) {
        await this.delay();
        const data = this.getPersonnelData();

        // Ensure agents array exists
        if (!data.agents) {
            data.agents = [];
            data.next_agent_id = 1;
        }

        // Check for duplicate email or agent_code
        const existingEmail = data.agents.find(a => a.email === agentData.email);
        if (existingEmail) {
            throw new Error('Email already exists');
        }

        const existingCode = data.agents.find(a => a.agent_code === agentData.agent_code);
        if (existingCode) {
            throw new Error('Agent code already exists');
        }

        const newAgent = {
            id: data.next_agent_id,
            ...agentData,
            wallet_balance: 0,
            total_earned: 0,
            total_paid: 0,
            last_activity: null,
            created_at: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.agents.push(newAgent);
        data.next_agent_id += 1;
        this.updatePersonnelData(data);

        return newAgent;
    }

    // Update agent
    async updateAgent(id, agentData) {
        await this.delay();
        const data = this.getPersonnelData();

        if (!data.agents) {
            throw new Error('Agent not found');
        }

        const agentIndex = data.agents.findIndex(a => a.id === parseInt(id));

        if (agentIndex === -1) {
            throw new Error('Agent not found');
        }

        // Check for duplicate email or agent_code (excluding current record)
        if (agentData.email) {
            const existingEmail = data.agents.find(a => a.email === agentData.email && a.id !== parseInt(id));
            if (existingEmail) {
                throw new Error('Email already exists');
            }
        }

        if (agentData.agent_code) {
            const existingCode = data.agents.find(a => a.agent_code === agentData.agent_code && a.id !== parseInt(id));
            if (existingCode) {
                throw new Error('Agent code already exists');
            }
        }

        const updatedAgent = {
            ...data.agents[agentIndex],
            ...agentData,
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.agents[agentIndex] = updatedAgent;
        this.updatePersonnelData(data);

        return updatedAgent;
    }

    // Delete agent
    async deleteAgent(id) {
        await this.delay();
        const data = this.getPersonnelData();

        if (!data.agents) {
            throw new Error('Agent not found');
        }

        const agentIndex = data.agents.findIndex(a => a.id === parseInt(id));

        if (agentIndex === -1) {
            throw new Error('Agent not found');
        }

        data.agents.splice(agentIndex, 1);
        this.updatePersonnelData(data);

        return { success: true, message: 'Agent deleted successfully' };
    }

    // Get active agents for dropdowns
    async getActiveAgents() {
        await this.delay();
        const data = this.getPersonnelData();
        return (data.agents || [])
            .filter(agent => agent.status === 'active')
            .map(agent => ({
                id: agent.id,
                name: agent.name,
                agent_code: agent.agent_code
            }));
    }
}

const personnelService = new PersonnelService();
export default personnelService;
