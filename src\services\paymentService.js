class PaymentService {
    constructor() {
        this.storageKey = 'activity_crm_payments';
        this.initializeData();
    }

    initializeData() {
        const existingData = localStorage.getItem(this.storageKey);
        if (!existingData) {
            const initialData = {
                payments: [],
                admin_wallet: {
                    total_received: 0,
                    pending_commission: 0,
                    total_commission_paid: 0
                },
                elephanta_staff_wallet: {
                    total_earned: 0,
                    pending_amount: 0,
                    total_paid: 0
                },
                next_payment_id: 1
            };
            localStorage.setItem(this.storageKey, JSON.stringify(initialData));
        }
    }

    getPaymentData() {
        const data = localStorage.getItem(this.storageKey);
        return data ? JSON.parse(data) : this.initializeData();
    }

    updatePaymentData(data) {
        localStorage.setItem(this.storageKey, JSON.stringify(data));
    }

    // Simulate API delay
    delay() {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    // Process payment collection from participant
    async processPaymentCollection(slotId, participantId, amount, collectedBy) {
        await this.delay();
        const data = this.getPaymentData();

        const payment = {
            id: data.next_payment_id,
            slot_id: slotId,
            participant_id: participantId,
            amount: amount,
            type: 'collection',
            status: 'collected',
            collected_by: collectedBy.id,
            collected_by_name: collectedBy.name,
            collected_at: new Date().toISOString(),
            transferred_to_admin: false,
            commission_calculated: false
        };

        data.payments.push(payment);
        data.next_payment_id += 1;

        // Update Elephanta staff pending amount
        data.elephanta_staff_wallet.pending_amount += amount;

        this.updatePaymentData(data);
        return payment;
    }

    // Transfer collected money to admin
    async transferToAdmin(paymentIds, elephantaStaff, transferMode = 'cash') {
        await this.delay();
        const data = this.getPaymentData();

        let totalAmount = 0;
        let totalCommission = 0;
        let bookingCount = 0;

        // Update payment records
        paymentIds.forEach(paymentId => {
            const payment = data.payments.find(p => p.id === paymentId);
            if (payment && !payment.transferred_to_admin) {
                payment.transferred_to_admin = true;
                payment.transferred_at = new Date().toISOString();
                payment.transfer_mode = transferMode;

                totalAmount += payment.amount;
                bookingCount++;

                // Fixed commission amount per booking
                const commissionPerBooking = elephantaStaff.commission_amount || 350;
                totalCommission += commissionPerBooking;

                payment.commission_amount = commissionPerBooking;
                payment.commission_calculated = true;
            }
        });

        // Update admin wallet
        data.admin_wallet.total_received += totalAmount;
        data.admin_wallet.pending_commission += totalCommission;

        // Update Elephanta staff wallet
        data.elephanta_staff_wallet.pending_amount -= totalAmount;
        data.elephanta_staff_wallet.total_earned += totalCommission;

        // Create transfer record
        const transfer = {
            id: data.next_payment_id,
            type: 'transfer_to_admin',
            amount: totalAmount,
            commission_amount: totalCommission,
            booking_count: bookingCount,
            commission_per_booking: elephantaStaff.commission_amount || 350,
            transfer_mode: transferMode,
            payment_ids: paymentIds,
            transferred_by: elephantaStaff.id,
            transferred_by_name: elephantaStaff.name,
            transferred_at: new Date().toISOString(),
            status: 'completed'
        };

        data.payments.push(transfer);
        data.next_payment_id += 1;

        this.updatePaymentData(data);
        return transfer;
    }

    // Process refund for cancelled trips
    async processRefund(slotId, participantId, amount, refundedBy, reason) {
        await this.delay();
        const data = this.getPaymentData();

        // Find the original payment
        const originalPayment = data.payments.find(p => 
            p.slot_id === slotId && 
            p.participant_id === participantId && 
            p.type === 'collection'
        );

        if (!originalPayment) {
            throw new Error('Original payment not found');
        }

        if (originalPayment.refunded) {
            throw new Error('Payment already refunded');
        }

        // Create refund record
        const refund = {
            id: data.next_payment_id,
            slot_id: slotId,
            participant_id: participantId,
            original_payment_id: originalPayment.id,
            amount: amount,
            type: 'refund',
            reason: reason,
            status: 'refunded',
            refunded_by: refundedBy.id,
            refunded_by_name: refundedBy.name,
            refunded_at: new Date().toISOString()
        };

        data.payments.push(refund);
        data.next_payment_id += 1;

        // Mark original payment as refunded
        originalPayment.refunded = true;
        originalPayment.refund_id = refund.id;

        // Adjust wallets if money was already transferred
        if (originalPayment.transferred_to_admin) {
            data.admin_wallet.total_received -= amount;
            
            if (originalPayment.commission_calculated) {
                const commission = originalPayment.commission_amount || 0;
                data.admin_wallet.pending_commission -= commission;
                data.elephanta_staff_wallet.total_earned -= commission;
            }
        } else {
            // Reduce pending amount if not yet transferred
            data.elephanta_staff_wallet.pending_amount -= amount;
        }

        this.updatePaymentData(data);
        return refund;
    }

    // Get payment summary for Elephanta staff
    async getElephantaStaffSummary(staffId) {
        await this.delay();
        const data = this.getPaymentData();

        const staffPayments = data.payments.filter(p => 
            p.collected_by === staffId || p.transferred_by === staffId
        );

        const summary = {
            total_collected: staffPayments
                .filter(p => p.type === 'collection' && !p.refunded)
                .reduce((sum, p) => sum + p.amount, 0),
            
            pending_transfer: data.elephanta_staff_wallet.pending_amount,
            
            total_commission_earned: data.elephanta_staff_wallet.total_earned,
            
            total_refunded: staffPayments
                .filter(p => p.type === 'refund')
                .reduce((sum, p) => sum + p.amount, 0),
            
            recent_payments: staffPayments
                .sort((a, b) => new Date(b.collected_at || b.transferred_at || b.refunded_at) - 
                              new Date(a.collected_at || a.transferred_at || a.refunded_at))
                .slice(0, 10)
        };

        return summary;
    }

    // Get admin payment summary
    async getAdminSummary() {
        await this.delay();
        const data = this.getPaymentData();

        const summary = {
            total_received: data.admin_wallet.total_received,
            pending_commission: data.admin_wallet.pending_commission,
            total_commission_paid: data.admin_wallet.total_commission_paid,
            
            recent_transfers: data.payments
                .filter(p => p.type === 'transfer_to_admin')
                .sort((a, b) => new Date(b.transferred_at) - new Date(a.transferred_at))
                .slice(0, 10),
            
            total_refunds: data.payments
                .filter(p => p.type === 'refund')
                .reduce((sum, p) => sum + p.amount, 0)
        };

        return summary;
    }

    // Get pending payments for transfer
    async getPendingPayments(staffId) {
        await this.delay();
        const data = this.getPaymentData();

        const pendingPayments = data.payments.filter(p => 
            p.type === 'collection' && 
            p.collected_by === staffId && 
            !p.transferred_to_admin && 
            !p.refunded
        );

        return pendingPayments;
    }

    // Pay commission to Elephanta staff
    async payCommissionToStaff(staffId, amount, paidBy) {
        await this.delay();
        const data = this.getPaymentData();

        if (amount > data.admin_wallet.pending_commission) {
            throw new Error('Insufficient commission balance');
        }

        const payment = {
            id: data.next_payment_id,
            type: 'commission_payment',
            amount: amount,
            paid_to: staffId,
            paid_by: paidBy.id,
            paid_by_name: paidBy.name,
            paid_at: new Date().toISOString(),
            status: 'completed'
        };

        data.payments.push(payment);
        data.next_payment_id += 1;

        // Update wallets
        data.admin_wallet.pending_commission -= amount;
        data.admin_wallet.total_commission_paid += amount;
        data.elephanta_staff_wallet.total_paid += amount;

        this.updatePaymentData(data);
        return payment;
    }
}

const paymentService = new PaymentService();
export default paymentService;
