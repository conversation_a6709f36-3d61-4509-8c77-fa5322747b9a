import React, { useState, useEffect, useCallback } from 'react';
import { XMarkIcon, CalculatorIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import activityService from '../../services/activityService';
import personnelService from '../../services/personnelService';
import bookingService from '../../services/bookingService';
import toast from 'react-hot-toast';

const BookingForm = ({ isOpen, onClose, onSuccess }) => {
    const { user } = useAuth();
    const [formData, setFormData] = useState({
        guest_name: '',
        contact_number: '',
        booking_date: '',
        activity_id: '',
        booking_type: 'direct',
        agency_id: '',
        boat_boy_id: '',
        number_of_adults: 1,
        number_of_children: 0
    });
    const [loading, setLoading] = useState(false);
    const [activities, setActivities] = useState([]);
    const [agencies, setAgencies] = useState([]);
    const [boatBoys, setBoatBoys] = useState([]);
    const [selectedActivity, setSelectedActivity] = useState(null);
    const [commissionPreview, setCommissionPreview] = useState(null);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (isOpen) {
            fetchFormData();
            // Set default booking date to today
            setFormData(prev => ({
                ...prev,
                booking_date: new Date().toISOString().split('T')[0]
            }));
        }
    }, [isOpen]);

    const fetchFormData = async () => {
        try {
            const [activitiesRes, agenciesRes, boatBoysRes] = await Promise.all([
                activityService.getActiveActivities(),
                bookingService.getAgencies(),
                personnelService.getActiveBoatBoys()
            ]);

            setActivities(activitiesRes);
            setAgencies(agenciesRes);
            setBoatBoys(boatBoysRes);
        } catch (error) {
            toast.error('Failed to load form data');
        }
    };

    const calculateCommissionPreview = useCallback(() => {
        if (!selectedActivity || user?.role === 'staff') return;

        const commissions = bookingService.calculateCommissions(
            selectedActivity,
            formData.booking_type,
            18,
            parseInt(formData.number_of_adults) || 1,
            parseInt(formData.number_of_children) || 0
        );
        setCommissionPreview(commissions);
    }, [selectedActivity, formData.booking_type, formData.number_of_adults, formData.number_of_children, user?.role]);

    useEffect(() => {
        if (selectedActivity && formData.booking_type && user?.role !== 'staff') {
            calculateCommissionPreview();
        }
    }, [selectedActivity, formData.booking_type, formData.number_of_adults, formData.number_of_children, user?.role, calculateCommissionPreview]);

    const handleActivityChange = (activityId) => {
        const activity = activities.find(a => a.id === parseInt(activityId));
        setSelectedActivity(activity);
        setFormData(prev => ({ ...prev, activity_id: activityId }));
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: undefined }));
        }

        // Reset agency when switching to direct booking
        if (field === 'booking_type' && value === 'direct') {
            setFormData(prev => ({ ...prev, agency_id: '' }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.guest_name.trim()) {
            newErrors.guest_name = 'Guest name is required';
        }

        if (!formData.contact_number.trim()) {
            newErrors.contact_number = 'Contact number is required';
        } else if (!/^\+?[\d\s-()]+$/.test(formData.contact_number)) {
            newErrors.contact_number = 'Invalid contact number format';
        }

        const numberOfAdults = parseInt(formData.number_of_adults);
        if (!formData.number_of_adults || isNaN(numberOfAdults) || numberOfAdults < 1) {
            newErrors.number_of_adults = 'At least 1 adult is required';
        }

        const numberOfChildren = parseInt(formData.number_of_children);
        if (formData.number_of_children && (isNaN(numberOfChildren) || numberOfChildren < 0)) {
            newErrors.number_of_children = 'Number of children must be 0 or greater';
        }

        if (!formData.booking_date) {
            newErrors.booking_date = 'Booking date is required';
        }

        if (!formData.activity_id) {
            newErrors.activity_id = 'Activity selection is required';
        }

        if (formData.booking_type === 'agency' && !formData.agency_id) {
            newErrors.agency_id = 'Agency selection is required for agency bookings';
        }

        if (!formData.boat_boy_id) {
            newErrors.boat_boy_id = 'Boat boy selection is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            // Get selected agency name if agency booking
            let agencyName = null;
            if (formData.booking_type === 'agency' && formData.agency_id) {
                const agency = agencies.find(a => a.id === parseInt(formData.agency_id));
                agencyName = agency?.name;
            }

            // Get selected boat boy name
            const boatBoy = boatBoys.find(b => b.id === parseInt(formData.boat_boy_id));
            const boatBoyName = boatBoy?.name;

            const bookingData = {
                ...formData,
                number_of_adults: parseInt(formData.number_of_adults),
                number_of_children: parseInt(formData.number_of_children) || 0,
                agency_name: agencyName,
                boat_boy_name: boatBoyName
            };

            const newBooking = await bookingService.createBooking(bookingData, user);
            toast.success(`Booking created successfully! Booking #${newBooking.booking_number}`);
            
            if (onSuccess) {
                onSuccess(newBooking);
            }
            
            onClose();
            resetForm();
        } catch (error) {
            toast.error(error.message || 'Failed to create booking');
        } finally {
            setLoading(false);
        }
    };

    const resetForm = () => {
        setFormData({
            guest_name: '',
            contact_number: '',
            booking_date: '',
            activity_id: '',
            booking_type: 'direct',
            agency_id: '',
            boat_boy_id: '',
            number_of_adults: 1,
            number_of_children: 0
        });
        setSelectedActivity(null);
        setCommissionPreview(null);
        setErrors({});
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-5xl bg-white rounded-xl shadow-2xl max-h-[90vh] overflow-hidden">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-teal-50 to-blue-50">
                    <div>
                        <h3 className="text-2xl font-bold text-gray-900">
                            Create New Booking
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">Fill in the details to create a new booking</p>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-2 rounded-full transition-colors"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                {/* Content */}
                <div className="overflow-y-auto max-h-[calc(90vh-140px)]">

                <form onSubmit={handleSubmit} className="p-6">
                    <div className={`grid grid-cols-1 gap-6 ${user?.role === 'staff' ? 'lg:grid-cols-2' : 'lg:grid-cols-3'}`}>
                        {/* Left Column - Guest Information */}
                        <div className="lg:col-span-1">
                            <div className="bg-gray-50 rounded-lg p-4">
                                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <div className="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center mr-3">
                                        <span className="text-teal-600 font-bold text-sm">1</span>
                                    </div>
                                    Guest Information
                                </h4>

                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Lead Guest Name *
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.guest_name}
                                            onChange={(e) => handleInputChange('guest_name', e.target.value)}
                                            className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                errors.guest_name ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                            } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                            placeholder="Enter lead guest name"
                                        />
                                        {errors.guest_name && <p className="mt-1 text-xs text-red-600">{errors.guest_name}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Contact Number *
                                        </label>
                                        <input
                                            type="tel"
                                            value={formData.contact_number}
                                            onChange={(e) => handleInputChange('contact_number', e.target.value)}
                                            className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                errors.contact_number ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                            } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                            placeholder="+91 9876543210"
                                        />
                                        {errors.contact_number && <p className="mt-1 text-xs text-red-600">{errors.contact_number}</p>}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Number of Adults *
                                            </label>
                                            <input
                                                type="number"
                                                value={formData.number_of_adults}
                                                onChange={(e) => handleInputChange('number_of_adults', e.target.value)}
                                                min="1"
                                                className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                    errors.number_of_adults ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                                } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                                placeholder="1"
                                            />
                                            {errors.number_of_adults && <p className="mt-1 text-xs text-red-600">{errors.number_of_adults}</p>}
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Number of Children
                                            </label>
                                            <input
                                                type="number"
                                                value={formData.number_of_children}
                                                onChange={(e) => handleInputChange('number_of_children', e.target.value)}
                                                min="0"
                                                className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                    errors.number_of_children ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                                } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                                placeholder="0"
                                            />
                                            {errors.number_of_children && <p className="mt-1 text-xs text-red-600">{errors.number_of_children}</p>}
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Booking Date *
                                        </label>
                                        <input
                                            type="date"
                                            value={formData.booking_date}
                                            onChange={(e) => handleInputChange('booking_date', e.target.value)}
                                            min={new Date().toISOString().split('T')[0]}
                                            max={new Date().toISOString().split('T')[0]}
                                            className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                errors.booking_date ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                            } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                        />
                                        {errors.booking_date && <p className="mt-1 text-xs text-red-600">{errors.booking_date}</p>}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Middle Column - Activity & Booking Details */}
                        <div className="lg:col-span-1">
                            <div className="bg-blue-50 rounded-lg p-4">
                                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <span className="text-blue-600 font-bold text-sm">2</span>
                                    </div>
                                    Activity & Details
                                </h4>

                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Select Activity *
                                        </label>
                                        <select
                                            value={formData.activity_id}
                                            onChange={(e) => handleActivityChange(e.target.value)}
                                            className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                errors.activity_id ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                            } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                        >
                                            <option value="">Select an activity</option>
                                            {activities.map((activity) => (
                                                <option key={activity.id} value={activity.id}>
                                                    {activity.name} - ₹{activity.price.toLocaleString()}
                                                </option>
                                            ))}
                                        </select>
                                        {errors.activity_id && <p className="mt-1 text-xs text-red-600">{errors.activity_id}</p>}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-3">
                                            Booking Type *
                                        </label>
                                        <div className="grid grid-cols-2 gap-3">
                                            <label className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all ${
                                                formData.booking_type === 'direct'
                                                    ? 'border-teal-500 bg-teal-50 text-teal-700'
                                                    : 'border-gray-200 hover:border-gray-300'
                                            }`}>
                                                <input
                                                    type="radio"
                                                    value="direct"
                                                    checked={formData.booking_type === 'direct'}
                                                    onChange={(e) => handleInputChange('booking_type', e.target.value)}
                                                    className="sr-only"
                                                />
                                                <div className="text-center w-full">
                                                    <div className="text-sm font-medium">Direct</div>
                                                    <div className="text-xs text-gray-500">No agency</div>
                                                </div>
                                            </label>
                                            <label className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all ${
                                                formData.booking_type === 'agency'
                                                    ? 'border-teal-500 bg-teal-50 text-teal-700'
                                                    : 'border-gray-200 hover:border-gray-300'
                                            }`}>
                                                <input
                                                    type="radio"
                                                    value="agency"
                                                    checked={formData.booking_type === 'agency'}
                                                    onChange={(e) => handleInputChange('booking_type', e.target.value)}
                                                    className="sr-only"
                                                />
                                                <div className="text-center w-full">
                                                    <div className="text-sm font-medium">Agency</div>
                                                    <div className="text-xs text-gray-500">Via partner</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    {formData.booking_type === 'agency' && (
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Select Agency *
                                            </label>
                                            <select
                                                value={formData.agency_id}
                                                onChange={(e) => handleInputChange('agency_id', e.target.value)}
                                                className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                    errors.agency_id ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                                } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                            >
                                                <option value="">Select an agency</option>
                                                {agencies.map((agency) => (
                                                    <option key={agency.id} value={agency.id}>
                                                        {agency.name}
                                                    </option>
                                                ))}
                                            </select>
                                            {errors.agency_id && <p className="mt-1 text-xs text-red-600">{errors.agency_id}</p>}
                                        </div>
                                    )}

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Select Boat Boy *
                                        </label>
                                        <select
                                            value={formData.boat_boy_id}
                                            onChange={(e) => handleInputChange('boat_boy_id', e.target.value)}
                                            className={`w-full border rounded-lg px-4 py-3 text-sm ${
                                                errors.boat_boy_id ? 'border-red-300 bg-red-50' : 'border-gray-200'
                                            } focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all`}
                                        >
                                            <option value="">Select a boat boy</option>
                                            {boatBoys.map((boatBoy) => (
                                                <option key={boatBoy.id} value={boatBoy.id}>
                                                    {boatBoy.name} ({boatBoy.boat_license})
                                                </option>
                                            ))}
                                        </select>
                                        {errors.boat_boy_id && <p className="mt-1 text-xs text-red-600">{errors.boat_boy_id}</p>}
                                    </div>

                                    <div className="bg-teal-50 border border-teal-200 p-3 rounded-lg">
                                        <div className="text-sm text-teal-700 font-medium">
                                            📋 Staff: {user?.name} (Auto-captured)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Commission Preview (Hidden for Staff) */}
                        {user?.role !== 'staff' && (
                            <div className="lg:col-span-1">
                                {selectedActivity && (
                                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
                                        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <CalculatorIcon className="h-4 w-4 text-green-600" />
                                            </div>
                                            Commission Preview
                                        </h4>

                                    <div className="space-y-4">
                                        {/* Activity Price Card */}
                                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                                            <h5 className="text-sm font-semibold text-gray-700 mb-3">💵 Pricing Breakdown</h5>

                                            <div className="flex justify-between items-center py-1">
                                                <span className="text-sm text-gray-600">Adults ({formData.number_of_adults || 1}) × ₹{selectedActivity.price}</span>
                                                <span className="text-sm font-medium">₹{((formData.number_of_adults || 1) * selectedActivity.price).toLocaleString()}</span>
                                            </div>

                                            {selectedActivity.child_price && (formData.number_of_children > 0) && (
                                                <div className="flex justify-between items-center py-1">
                                                    <span className="text-sm text-gray-600">Children ({formData.number_of_children}) × ₹{selectedActivity.child_price}</span>
                                                    <span className="text-sm font-medium">₹{(formData.number_of_children * selectedActivity.child_price).toLocaleString()}</span>
                                                </div>
                                            )}

                                            <div className="border-t border-gray-200 mt-2 pt-2">
                                                <div className="flex justify-between items-center">
                                                    <span className="text-sm font-medium text-gray-700">Total Price</span>
                                                    <span className="text-xl font-bold text-gray-900">
                                                        ₹{commissionPreview ? commissionPreview.total_price.toLocaleString() : selectedActivity.price.toLocaleString()}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        {commissionPreview && user?.role !== 'staff' && (
                                            <>
                                                {/* Commission Breakdown Card */}
                                                <div className="bg-white rounded-lg p-4 border border-gray-200">
                                                    <h5 className="text-sm font-semibold text-gray-700 mb-3">💰 Commission Breakdown</h5>

                                                    {formData.booking_type === 'agency' && (
                                                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                                            <span className="text-sm text-gray-600">🏢 Agent</span>
                                                            <span className="text-sm font-semibold text-purple-600">₹{commissionPreview.agent_commission}</span>
                                                        </div>
                                                    )}

                                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                                        <span className="text-sm text-gray-600">👤 Staff</span>
                                                        <span className="text-sm font-semibold text-blue-600">₹{commissionPreview.staff_commission}</span>
                                                    </div>

                                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                                        <span className="text-sm text-gray-600">⛵ Boat Boy</span>
                                                        <span className="text-sm font-semibold text-teal-600">₹{commissionPreview.boat_boy_commission}</span>
                                                    </div>

                                                    <div className="flex justify-between items-center py-2 bg-gray-50 rounded mt-2 px-2">
                                                        <span className="text-sm font-medium text-gray-700">Total Commission</span>
                                                        <span className="text-sm font-bold text-gray-900">₹{commissionPreview.total_commission}</span>
                                                    </div>
                                                </div>

                                                {/* Financial Breakdown Card */}
                                                <div className="bg-white rounded-lg p-4 border border-gray-200">
                                                    <h5 className="text-sm font-semibold text-gray-700 mb-3">📊 Financial Breakdown</h5>

                                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                                        <span className="text-sm text-gray-600">Remaining Amount</span>
                                                        <span className="text-sm font-medium">₹{commissionPreview.remaining_amount.toFixed(2)}</span>
                                                    </div>

                                                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                                                        <span className="text-sm text-gray-600">GST (18%)</span>
                                                        <span className="text-sm font-medium text-orange-600">₹{commissionPreview.gst_amount.toFixed(2)}</span>
                                                    </div>

                                                    <div className="flex justify-between items-center py-2 bg-green-50 rounded mt-2 px-2">
                                                        <span className="text-sm font-medium text-gray-700">💼 Admin Share</span>
                                                        <span className="text-sm font-bold text-green-600">₹{commissionPreview.admin_share.toFixed(2)}</span>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Form Actions */}
                    <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-8 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                        >
                            {loading ? (
                                <div className="flex items-center">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Creating...
                                </div>
                            ) : (
                                '✨ Create Booking'
                            )}
                        </button>
                    </div>
                </form>
                </div>
            </div>
        </div>
    );
};

export default BookingForm;
