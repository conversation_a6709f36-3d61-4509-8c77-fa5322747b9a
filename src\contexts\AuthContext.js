import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem('activity_crm_user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    // Demo login - in real app, this would be an API call
    const users = {
      'admin': {
        id: 1,
        name: 'System Admin',
        email: '<EMAIL>',
        username: 'admin',
        role: 'admin',
        status: 'active'
      },
      'manager': {
        id: 2,
        name: 'Operations Manager',
        email: '<EMAIL>',
        username: 'manager',
        role: 'manager',
        status: 'active'
      },
      'staff1': {
        id: 3,
        name: 'John Doe',
        email: '<EMAIL>',
        username: 'staff1',
        role: 'staff',
        status: 'active'
      },
      'agent1': {
        id: 4,
        name: 'ABC Tours',
        email: '<EMAIL>',
        username: 'agent1',
        role: 'agent',
        status: 'active'
      },
      'elephanta_staff': {
        id: 5,
        name: 'Elephanta Staff',
        email: '<EMAIL>',
        username: 'elephanta_staff',
        role: 'elephanta_staff',
        status: 'active',
        commission_amount: 350 // Fixed ₹350 commission per booking
      }
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const foundUser = users[username];
    if (foundUser && (password === 'admin123' || password === 'manager123' || password === 'staff123' || password === 'agent123' || password === 'elephanta123')) {
      setUser(foundUser);
      localStorage.setItem('activity_crm_user', JSON.stringify(foundUser));
      return { success: true, user: foundUser };
    } else {
      throw new Error('Invalid credentials');
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('activity_crm_user');
  };

  const hasRole = (roles) => {
    if (!user) return false;
    if (Array.isArray(roles)) {
      return roles.includes(user.role);
    }
    return user.role === roles;
  };

  const value = {
    user,
    login,
    logout,
    hasRole,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};