// Permission Management Service
// Handles role-based permissions and access control

class PermissionService {
    constructor() {
        this.initializePermissions();
    }

    // Initialize default permissions in localStorage
    initializePermissions() {
        if (!localStorage.getItem('activity_crm_permissions')) {
            const defaultPermissions = {
                manager_permissions: {
                    payment_release: false, // By default, manager cannot release payments
                    view_wallets: true,
                    manage_personnel: true,
                    manage_activities: true,
                    view_reports: true,
                    granted_by: null,
                    granted_at: null,
                    last_updated: new Date().toISOString()
                },
                permission_history: [
                    {
                        id: 1,
                        action: 'system_initialization',
                        permission: 'payment_release',
                        granted_to: 'manager',
                        granted_by: 'system',
                        status: 'denied',
                        timestamp: new Date().toISOString(),
                        reason: 'Default system setup - payment release restricted to admin only'
                    }
                ],
                next_history_id: 2
            };
            localStorage.setItem('activity_crm_permissions', JSON.stringify(defaultPermissions));
        }
    }

    // Get permissions data from localStorage
    getPermissionsData() {
        const data = localStorage.getItem('activity_crm_permissions');
        return data ? JSON.parse(data) : this.getDefaultPermissions();
    }

    // Update permissions data in localStorage
    updatePermissionsData(data) {
        localStorage.setItem('activity_crm_permissions', JSON.stringify(data));
    }

    // Get default permissions structure
    getDefaultPermissions() {
        return {
            manager_permissions: {
                payment_release: false,
                view_wallets: true,
                manage_personnel: true,
                manage_activities: true,
                view_reports: true,
                granted_by: null,
                granted_at: null,
                last_updated: new Date().toISOString()
            },
            permission_history: [],
            next_history_id: 1
        };
    }

    // Simulate API delay
    delay(ms = 300) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Check if manager has specific permission
    async hasPermission(role, permission) {
        await this.delay();
        
        // Admin always has all permissions
        if (role === 'admin') {
            return true;
        }

        // For manager role, check specific permissions
        if (role === 'manager') {
            const data = this.getPermissionsData();
            return data.manager_permissions[permission] || false;
        }

        // Other roles don't have admin-level permissions
        return false;
    }

    // Get all manager permissions
    async getManagerPermissions() {
        await this.delay();
        const data = this.getPermissionsData();
        return data.manager_permissions;
    }

    // Grant permission to manager (only admin can do this)
    async grantPermission(permission, grantedBy, reason = '') {
        await this.delay();
        
        const data = this.getPermissionsData();
        
        // Update permission
        data.manager_permissions[permission] = true;
        data.manager_permissions.granted_by = grantedBy;
        data.manager_permissions.granted_at = new Date().toISOString();
        data.manager_permissions.last_updated = new Date().toISOString();

        // Add to history
        const historyEntry = {
            id: data.next_history_id,
            action: 'permission_granted',
            permission: permission,
            granted_to: 'manager',
            granted_by: grantedBy,
            status: 'granted',
            timestamp: new Date().toISOString(),
            reason: reason || `Permission granted by ${grantedBy}`
        };

        data.permission_history.push(historyEntry);
        data.next_history_id += 1;

        this.updatePermissionsData(data);

        return {
            success: true,
            message: `Permission '${permission}' granted to manager successfully`,
            permission: permission,
            granted_at: new Date().toISOString()
        };
    }

    // Revoke permission from manager (only admin can do this)
    async revokePermission(permission, revokedBy, reason = '') {
        await this.delay();
        
        const data = this.getPermissionsData();
        
        // Update permission
        data.manager_permissions[permission] = false;
        data.manager_permissions.granted_by = null;
        data.manager_permissions.granted_at = null;
        data.manager_permissions.last_updated = new Date().toISOString();

        // Add to history
        const historyEntry = {
            id: data.next_history_id,
            action: 'permission_revoked',
            permission: permission,
            granted_to: 'manager',
            granted_by: revokedBy,
            status: 'revoked',
            timestamp: new Date().toISOString(),
            reason: reason || `Permission revoked by ${revokedBy}`
        };

        data.permission_history.push(historyEntry);
        data.next_history_id += 1;

        this.updatePermissionsData(data);

        return {
            success: true,
            message: `Permission '${permission}' revoked from manager successfully`,
            permission: permission,
            revoked_at: new Date().toISOString()
        };
    }

    // Get permission history
    async getPermissionHistory(limit = 10) {
        await this.delay();
        const data = this.getPermissionsData();
        
        return data.permission_history
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, limit);
    }

    // Get all available permissions that can be granted
    getAvailablePermissions() {
        return [
            {
                key: 'payment_release',
                name: 'Payment Release',
                description: 'Allow manager to release payments to staff, boat boys, and agents',
                category: 'Financial',
                risk_level: 'high'
            },
            {
                key: 'view_wallets',
                name: 'View Wallets',
                description: 'View wallet balances and transaction history',
                category: 'Financial',
                risk_level: 'low'
            },
            {
                key: 'manage_personnel',
                name: 'Manage Personnel',
                description: 'Add, edit, and manage staff and boat boys',
                category: 'Personnel',
                risk_level: 'medium'
            },
            {
                key: 'manage_activities',
                name: 'Manage Activities',
                description: 'Create and modify activities and pricing',
                category: 'Operations',
                risk_level: 'medium'
            },
            {
                key: 'view_reports',
                name: 'View Reports',
                description: 'Access financial and operational reports',
                category: 'Reports',
                risk_level: 'low'
            }
        ];
    }

    // Check multiple permissions at once
    async checkPermissions(role, permissions) {
        await this.delay();
        
        const results = {};
        for (const permission of permissions) {
            results[permission] = await this.hasPermission(role, permission);
        }
        
        return results;
    }

    // Get permission summary for dashboard
    async getPermissionSummary(role) {
        await this.delay();
        
        if (role === 'admin') {
            return {
                role: 'admin',
                has_all_permissions: true,
                total_permissions: this.getAvailablePermissions().length,
                granted_permissions: this.getAvailablePermissions().length,
                restricted_permissions: 0
            };
        }

        if (role === 'manager') {
            const permissions = await this.getManagerPermissions();
            const available = this.getAvailablePermissions();
            const granted = available.filter(p => permissions[p.key]).length;
            
            return {
                role: 'manager',
                has_all_permissions: false,
                total_permissions: available.length,
                granted_permissions: granted,
                restricted_permissions: available.length - granted,
                last_updated: permissions.last_updated
            };
        }

        return {
            role: role,
            has_all_permissions: false,
            total_permissions: 0,
            granted_permissions: 0,
            restricted_permissions: 0
        };
    }
}

const permissionService = new PermissionService();
export default permissionService;
