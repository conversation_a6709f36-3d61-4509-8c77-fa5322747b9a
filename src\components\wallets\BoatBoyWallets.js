import React, { useState, useEffect } from 'react';
import walletService from '../../services/walletService';
import PaymentModal from './PaymentModal';
import { 
    CurrencyDollarIcon,
    BanknotesIcon,
    ClockIcon,
    UserIcon
} from '@heroicons/react/24/outline';

const BoatBoyWallets = ({ onPaymentSuccess, canProcessPayments = true }) => {
    const [boatBoyWallets, setBoatBoyWallets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedWallet, setSelectedWallet] = useState(null);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [summary, setSummary] = useState(null);

    useEffect(() => {
        fetchBoatBoyWallets();
    }, []);

    const fetchBoatBoyWallets = async () => {
        try {
            setLoading(true);
            const response = await walletService.getBoatBoyWallets();
            setBoatBoyWallets(response.boat_boy_wallets || []);
            setSummary({
                total_boat_boys: response.total_boat_boys || 0,
                total_balance: response.total_balance || 0,
                total_pending: response.total_pending || 0
            });
        } catch (error) {
            console.error('Failed to fetch boat boy wallets:', error);
            // Set empty data on error
            setBoatBoyWallets([]);
            setSummary({
                total_boat_boys: 0,
                total_balance: 0,
                total_pending: 0
            });
        } finally {
            setLoading(false);
        }
    };

    const handleReleasePayment = (wallet) => {
        if (wallet.balance <= 0) {
            alert('No balance available for payment');
            return;
        }
        setSelectedWallet(wallet);
        setShowPaymentModal(true);
    };

    const handlePaymentSuccess = () => {
        setShowPaymentModal(false);
        setSelectedWallet(null);
        fetchBoatBoyWallets();
        if (onPaymentSuccess) {
            onPaymentSuccess();
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Summary Cards */}
            {summary && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <UserIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Boat Boys</p>
                                <p className="text-2xl font-semibold text-gray-900">{summary.total_boat_boys}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Balance</p>
                                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(summary.total_balance)}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ClockIcon className="h-8 w-8 text-yellow-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Pending Payments</p>
                                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(summary.total_pending)}</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Boat Boy Wallets Table */}
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Boat Boy Wallets
                    </h3>
                    
                    {boatBoyWallets.length === 0 ? (
                        <div className="text-center py-8">
                            <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No boat boys found</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                No boat boy wallets are available at the moment.
                            </p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Boat Boy
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Balance
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Pending
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Last Activity
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {boatBoyWallets.map((wallet) => (
                                        <tr key={wallet.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                            <UserIcon className="h-6 w-6 text-blue-600" />
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {wallet.name || 'Unknown Boat Boy'}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            ID: {wallet.id}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {formatCurrency(wallet.balance || 0)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {formatCurrency(wallet.pending || 0)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {wallet.last_activity ? new Date(wallet.last_activity).toLocaleDateString() : 'N/A'}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                {canProcessPayments ? (
                                                    <button
                                                        onClick={() => handleReleasePayment(wallet)}
                                                        disabled={wallet.balance <= 0}
                                                        className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${
                                                            wallet.balance > 0
                                                                ? 'text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                                                                : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                                                        }`}
                                                    >
                                                        <BanknotesIcon className="h-4 w-4 mr-1" />
                                                        Release Payment
                                                    </button>
                                                ) : (
                                                    <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
                                                        <BanknotesIcon className="h-4 w-4 mr-1" />
                                                        Access Restricted
                                                    </span>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>

            {/* Payment Modal */}
            {showPaymentModal && selectedWallet && (
                <PaymentModal
                    wallet={selectedWallet}
                    onClose={() => setShowPaymentModal(false)}
                    onSuccess={handlePaymentSuccess}
                    walletType="boat_boy"
                />
            )}
        </div>
    );
};

export default BoatBoyWallets;
