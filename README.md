# Activity CRM Frontend

Complete React frontend application for Activity CRM with wallet management functionality.

## 🚀 Quick Start

### Installation
```bash
npm install
npm start
```

Application available at: `http://localhost:3000`

## 🔑 Demo Credentials

| Role | Email | Password |
|------|-------|----------|
| **Admin** | <EMAIL> | admin123 |
| **Manager** | <EMAIL> | manager123 |
| **Staff** | <EMAIL> | staff123 |
| **Agent** | <EMAIL> | agent123 |

## 📱 Features

- **Dashboard** with metrics and recent activity
- **Activity Management** (Admin/Manager only)
- **Booking Management** with status tracking
- **Wallet Management** with payment release
  - Staff Wallets
  - Boat Boy Wallets
  - Agent Wallets
  - Payment Reports with CSV export
- **Role-based Access Control**
- **Responsive Design**

## 🛠️ Tech Stack

- React 18.2
- Tailwind CSS 3.2
- React Router DOM 6.8
- Heroicons React
- localStorage (demo mode)

## 📊 Demo Mode

All data is stored in localStorage for demonstration. Payment releases and wallet updates work in real-time without requiring a backend.

Ready for production backend integration when needed!