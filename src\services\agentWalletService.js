// Agent Wallet Service
// Handles agent wallet operations and commission tracking

class AgentWalletService {
    constructor() {
        this.initializeSampleData();
    }

    // Initialize sample data in localStorage
    initializeSampleData() {
        if (!localStorage.getItem('activity_crm_agent_wallets')) {
            const sampleData = {
                agent_wallets: [
                    {
                        id: 1,
                        agent_id: 4, // Matches the agent user ID from AuthContext
                        agent_name: 'ABC Tours',
                        agent_email: '<EMAIL>',
                        balance: 1250.00,
                        total_earned: 5600.00,
                        total_paid: 4350.00,
                        pending_amount: 0.00,
                        last_transaction: '2025-01-06',
                        created_at: '2024-01-01',
                        updated_at: '2025-01-06'
                    }
                ],
                transactions: [
                    {
                        id: 1,
                        agent_id: 4,
                        booking_id: 1,
                        booking_number: 'BK202500001',
                        activity_name: 'Dinner Cruise',
                        guest_name: '<PERSON>',
                        commission_amount: 350.00,
                        transaction_type: 'commission_earned',
                        status: 'completed',
                        transaction_date: '2025-01-06',
                        created_at: '2025-01-06T10:30:00Z'
                    },
                    {
                        id: 2,
                        agent_id: 4,
                        booking_id: null,
                        booking_number: null,
                        activity_name: null,
                        guest_name: null,
                        commission_amount: 1500.00,
                        transaction_type: 'payment_received',
                        status: 'completed',
                        transaction_date: '2025-01-05',
                        created_at: '2025-01-05T15:20:00Z',
                        payment_method: 'Bank Transfer',
                        payment_reference: 'TXN123456789'
                    },
                    {
                        id: 3,
                        agent_id: 4,
                        booking_id: 5,
                        booking_number: 'BK202500005',
                        activity_name: 'Island Hopping',
                        guest_name: 'Sarah Wilson',
                        commission_amount: 280.00,
                        transaction_type: 'commission_earned',
                        status: 'completed',
                        transaction_date: '2025-01-04',
                        created_at: '2025-01-04T11:15:00Z'
                    }
                ],
                next_transaction_id: 4
            };
            localStorage.setItem('activity_crm_agent_wallets', JSON.stringify(sampleData));
        }
    }

    // Get wallet data from localStorage
    getWalletData() {
        const data = localStorage.getItem('activity_crm_agent_wallets');
        return data ? JSON.parse(data) : { 
            agent_wallets: [], 
            transactions: [],
            next_transaction_id: 1
        };
    }

    // Update wallet data in localStorage
    updateWalletData(data) {
        localStorage.setItem('activity_crm_agent_wallets', JSON.stringify(data));
    }

    // Simulate API delay
    delay(ms = 500) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get agent wallet by agent ID
    async getAgentWallet(agentId) {
        await this.delay();
        const data = this.getWalletData();
        
        let wallet = data.agent_wallets.find(w => w.agent_id === parseInt(agentId));
        
        // If wallet doesn't exist, create one
        if (!wallet) {
            wallet = {
                id: data.agent_wallets.length + 1,
                agent_id: parseInt(agentId),
                agent_name: 'Agent User',
                agent_email: '<EMAIL>',
                balance: 0.00,
                total_earned: 0.00,
                total_paid: 0.00,
                pending_amount: 0.00,
                last_transaction: null,
                created_at: new Date().toISOString().split('T')[0],
                updated_at: new Date().toISOString().split('T')[0]
            };
            data.agent_wallets.push(wallet);
            this.updateWalletData(data);
        }

        return wallet;
    }

    // Get agent transactions
    async getAgentTransactions(agentId, filters = {}) {
        await this.delay();
        const data = this.getWalletData();
        let transactions = data.transactions.filter(t => t.agent_id === parseInt(agentId));

        // Apply filters
        if (filters.transaction_type) {
            transactions = transactions.filter(t => t.transaction_type === filters.transaction_type);
        }
        if (filters.status) {
            transactions = transactions.filter(t => t.status === filters.status);
        }
        if (filters.date_from) {
            transactions = transactions.filter(t => t.transaction_date >= filters.date_from);
        }
        if (filters.date_to) {
            transactions = transactions.filter(t => t.transaction_date <= filters.date_to);
        }

        // Sort by date (newest first)
        transactions.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        return {
            transactions,
            total: transactions.length,
            total_commission_earned: transactions
                .filter(t => t.transaction_type === 'commission_earned')
                .reduce((sum, t) => sum + t.commission_amount, 0),
            total_payments_received: transactions
                .filter(t => t.transaction_type === 'payment_received')
                .reduce((sum, t) => sum + t.commission_amount, 0)
        };
    }

    // Add commission from booking
    async addCommissionFromBooking(booking) {
        await this.delay();
        const data = this.getWalletData();

        // Only process if it's an agency booking with agent commission
        if (booking.booking_type !== 'agency' || !booking.commission_breakdown.agent_commission) {
            return;
        }

        // Find or create agent wallet
        let wallet = data.agent_wallets.find(w => w.agent_id === booking.agency_id);
        if (!wallet) {
            wallet = {
                id: data.agent_wallets.length + 1,
                agent_id: booking.agency_id,
                agent_name: booking.agency_name,
                agent_email: '<EMAIL>',
                balance: 0.00,
                total_earned: 0.00,
                total_paid: 0.00,
                pending_amount: 0.00,
                last_transaction: null,
                created_at: new Date().toISOString().split('T')[0],
                updated_at: new Date().toISOString().split('T')[0]
            };
            data.agent_wallets.push(wallet);
        }

        // Add commission transaction
        const transaction = {
            id: data.next_transaction_id,
            agent_id: booking.agency_id,
            booking_id: booking.id,
            booking_number: booking.booking_number,
            activity_name: booking.activity_name,
            guest_name: booking.guest_name,
            commission_amount: booking.commission_breakdown.agent_commission,
            transaction_type: 'commission_earned',
            status: 'completed',
            transaction_date: booking.booking_date,
            created_at: new Date().toISOString()
        };

        data.transactions.push(transaction);
        data.next_transaction_id += 1;

        // Update wallet balance
        wallet.balance += booking.commission_breakdown.agent_commission;
        wallet.total_earned += booking.commission_breakdown.agent_commission;
        wallet.last_transaction = new Date().toISOString().split('T')[0];
        wallet.updated_at = new Date().toISOString().split('T')[0];

        this.updateWalletData(data);

        return transaction;
    }

    // Get wallet statistics for dashboard
    async getWalletStats(agentId) {
        await this.delay();
        const data = this.getWalletData();
        const wallet = await this.getAgentWallet(agentId);
        const transactions = data.transactions.filter(t => t.agent_id === parseInt(agentId));

        const today = new Date().toISOString().split('T')[0];
        const thisMonth = new Date().toISOString().slice(0, 7);

        const todayCommissions = transactions
            .filter(t => t.transaction_type === 'commission_earned' && t.transaction_date === today)
            .reduce((sum, t) => sum + t.commission_amount, 0);

        const monthCommissions = transactions
            .filter(t => t.transaction_type === 'commission_earned' && t.transaction_date.startsWith(thisMonth))
            .reduce((sum, t) => sum + t.commission_amount, 0);

        const totalBookings = transactions
            .filter(t => t.transaction_type === 'commission_earned').length;

        return {
            current_balance: wallet.balance,
            total_earned: wallet.total_earned,
            total_paid: wallet.total_paid,
            pending_amount: wallet.pending_amount,
            today_commission: todayCommissions,
            month_commission: monthCommissions,
            total_bookings: totalBookings,
            last_transaction: wallet.last_transaction
        };
    }

    // Request payment (for future implementation)
    async requestPayment(agentId, amount, paymentMethod = 'bank_transfer') {
        await this.delay();
        // This would typically create a payment request
        // For now, we'll just return a success message
        return {
            success: true,
            message: 'Payment request submitted successfully',
            request_id: `PAY${Date.now()}`,
            amount: amount,
            status: 'pending'
        };
    }

    // Get recent transactions for dashboard
    async getRecentTransactions(agentId, limit = 5) {
        await this.delay();
        const data = this.getWalletData();
        const transactions = data.transactions
            .filter(t => t.agent_id === parseInt(agentId))
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
            .slice(0, limit);

        return transactions;
    }
}

const agentWalletService = new AgentWalletService();
export default agentWalletService;
