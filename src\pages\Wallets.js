import React, { useState, useEffect, useCallback } from 'react';
import walletService from '../services/walletService';
import permissionService from '../services/permissionService';
import StaffWallets from '../components/wallets/StaffWallets';
import BoatBoyWallets from '../components/wallets/BoatBoyWallets';
import AgentWallets from '../components/wallets/AgentWallets';
import PaymentReports from '../components/wallets/PaymentReports';
import PermissionManagement from '../components/admin/PermissionManagement';
import { useAuth } from '../contexts/AuthContext';
import {
  CurrencyDollarIcon,
  UsersIcon,
  ChartBarIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const Wallets = () => {
    const { user } = useAuth();
    const [activeTab, setActiveTab] = useState('staff');
    const [summary, setSummary] = useState(null);
    const [loading, setLoading] = useState(true);
    const [hasPaymentPermission, setHasPaymentPermission] = useState(false);
    const [permissionLoading, setPermissionLoading] = useState(true);

    const checkPermissions = useCallback(async () => {
        try {
            setPermissionLoading(true);
            const hasPermission = await permissionService.hasPermission(user?.role, 'payment_release');
            setHasPaymentPermission(hasPermission);
        } catch (error) {
            console.error('Failed to check permissions:', error);
            setHasPaymentPermission(false);
        } finally {
            setPermissionLoading(false);
        }
    }, [user?.role]);

    useEffect(() => {
        fetchWalletSummary();
        checkPermissions();
    }, [user, checkPermissions]);

    const fetchWalletSummary = async () => {
        try {
            setLoading(true);
            const summaryData = await walletService.getWalletSummary();
            setSummary(summaryData);
        } catch (error) {
            console.error('Failed to fetch wallet summary:', error);
        } finally {
            setLoading(false);
        }
    };

    // Dynamic tabs based on user role and permissions
    const getAvailableTabs = () => {
        const baseTabs = [
            { id: 'staff', label: 'Staff Wallets', icon: UsersIcon },
            { id: 'boat_boys', label: 'Boat Boy Wallets', icon: UsersIcon },
            { id: 'agents', label: 'Agent Wallets', icon: UsersIcon },
            { id: 'reports', label: 'Payment Reports', icon: ChartBarIcon }
        ];

        // Add permission management tab for admin only
        if (user?.role === 'admin') {
            baseTabs.push({
                id: 'permissions',
                label: 'Permission Management',
                icon: ShieldCheckIcon
            });
        }

        return baseTabs;
    };

    const tabs = getAvailableTabs();

    if (loading || permissionLoading) {
        return (
            <div className="flex items-center justify-center min-h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-teal-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="sm:flex sm:items-center sm:justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Wallet Management</h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Manage commission payments and track wallet balances
                    </p>
                </div>
                <div className="mt-4 sm:mt-0 flex items-center space-x-3">
                    {/* Permission Status Indicator */}
                    {user?.role === 'manager' && (
                        <span className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                            hasPaymentPermission
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                        }`}>
                            {hasPaymentPermission ? (
                                <>
                                    <ShieldCheckIcon className="h-4 w-4 mr-1" />
                                    Payment Access Granted
                                </>
                            ) : (
                                <>
                                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                                    Payment Access Restricted
                                </>
                            )}
                        </span>
                    )}

                    <span className="inline-flex items-center px-3 py-2 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
                        Demo Mode - Using Local Data
                    </span>
                </div>
            </div>

            {/* Summary Cards */}
            {summary && (
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                        <div className="p-5">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">
                                            Total Balance
                                        </dt>
                                        <dd className="text-lg font-medium text-gray-900">
                                            ₹{summary.total_balance?.toLocaleString() || '0'}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white overflow-hidden shadow rounded-lg">
                        <div className="p-5">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <BanknotesIcon className="h-6 w-6 text-blue-600" />
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">
                                            Total Earned
                                        </dt>
                                        <dd className="text-lg font-medium text-gray-900">
                                            ₹{summary.total_earned?.toLocaleString() || '0'}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white overflow-hidden shadow rounded-lg">
                        <div className="p-5">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <ChartBarIcon className="h-6 w-6 text-purple-600" />
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">
                                            Total Paid
                                        </dt>
                                        <dd className="text-lg font-medium text-gray-900">
                                            ₹{summary.total_paid?.toLocaleString() || '0'}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white overflow-hidden shadow rounded-lg">
                        <div className="p-5">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <UsersIcon className="h-6 w-6 text-orange-600" />
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt className="text-sm font-medium text-gray-500 truncate">
                                            Total Wallets
                                        </dt>
                                        <dd className="text-lg font-medium text-gray-900">
                                            {summary.total_wallets || 0}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Tab Navigation */}
            <div className="bg-white shadow">
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        {tabs.map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === tab.id
                                        ? 'border-teal-500 text-teal-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <tab.icon
                                    className={`mr-2 h-5 w-5 ${
                                        activeTab === tab.id ? 'text-teal-500' : 'text-gray-400'
                                    }`}
                                />
                                {tab.label}
                            </button>
                        ))}
                    </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                    {/* Show permission warning for managers without payment access */}
                    {user?.role === 'manager' && !hasPaymentPermission && (
                        activeTab === 'staff' || activeTab === 'boat_boys' || activeTab === 'agents'
                    ) && (
                        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div className="flex">
                                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                                <div className="text-sm text-yellow-700">
                                    <p className="font-medium">Payment Access Restricted</p>
                                    <p className="mt-1">
                                        You can view wallet information but cannot process payments.
                                        Contact your administrator to request payment release permissions.
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'staff' && (
                        <StaffWallets
                            onPaymentSuccess={fetchWalletSummary}
                            canProcessPayments={hasPaymentPermission}
                        />
                    )}
                    {activeTab === 'boat_boys' && (
                        <BoatBoyWallets
                            onPaymentSuccess={fetchWalletSummary}
                            canProcessPayments={hasPaymentPermission}
                        />
                    )}
                    {activeTab === 'agents' && (
                        <AgentWallets
                            onPaymentSuccess={fetchWalletSummary}
                            canProcessPayments={hasPaymentPermission}
                        />
                    )}
                    {activeTab === 'reports' && <PaymentReports />}
                    {activeTab === 'permissions' && user?.role === 'admin' && <PermissionManagement />}
                </div>
            </div>
        </div>
    );
};

export default Wallets;