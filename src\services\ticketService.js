class TicketService {
    constructor() {
        this.storageKey = 'activity_crm_tickets';
        this.initializeData();
    }

    initializeData() {
        const existingData = localStorage.getItem(this.storageKey);
        if (!existingData) {
            const initialData = {
                tickets: [
                    // Sample tickets for demo
                    {
                        id: 'TKT001',
                        customer_name: '<PERSON><PERSON>',
                        contact: '9876543210',
                        email: '<PERSON><PERSON><PERSON>@example.com',
                        adults: 2,
                        children: 1,
                        total_persons: 3,
                        total_amount: 10500,
                        booking_date: '2024-08-08',
                        status: 'confirmed',
                        created_at: '2024-08-08T10:00:00.000Z'
                    },
                    {
                        id: 'TKT002',
                        customer_name: '<PERSON><PERSON>',
                        contact: '9876543211',
                        email: '<EMAIL>',
                        adults: 4,
                        children: 2,
                        total_persons: 6,
                        total_amount: 21000,
                        booking_date: '2024-08-08',
                        status: 'confirmed',
                        created_at: '2024-08-08T11:00:00.000Z'
                    },
                    {
                        id: 'TKT003',
                        customer_name: '<PERSON><PERSON>',
                        contact: '9876543212',
                        email: '<EMAIL>',
                        adults: 1,
                        children: 0,
                        total_persons: 1,
                        total_amount: 3500,
                        booking_date: '2024-08-09',
                        status: 'confirmed',
                        created_at: '2024-08-08T12:00:00.000Z'
                    },
                    {
                        id: 'TKT004',
                        customer_name: 'Sunita Gupta',
                        contact: '9876543213',
                        email: '<EMAIL>',
                        adults: 3,
                        children: 1,
                        total_persons: 4,
                        total_amount: 14000,
                        booking_date: '2024-08-09',
                        status: 'confirmed',
                        created_at: '2024-08-08T13:00:00.000Z'
                    },
                    {
                        id: 'TKT005',
                        customer_name: 'Vikram Singh',
                        contact: '9876543214',
                        email: '<EMAIL>',
                        adults: 2,
                        children: 2,
                        total_persons: 4,
                        total_amount: 14000,
                        booking_date: '2024-08-10',
                        status: 'confirmed',
                        created_at: '2024-08-08T14:00:00.000Z'
                    }
                ],
                next_ticket_id: 6
            };
            localStorage.setItem(this.storageKey, JSON.stringify(initialData));
        }
    }

    getTicketData() {
        const data = localStorage.getItem(this.storageKey);
        return data ? JSON.parse(data) : this.initializeData();
    }

    updateTicketData(data) {
        localStorage.setItem(this.storageKey, JSON.stringify(data));
    }

    // Simulate API delay
    delay() {
        return new Promise(resolve => setTimeout(resolve, 300));
    }

    // Get ticket by ID
    async getTicketById(ticketId) {
        await this.delay();
        const data = this.getTicketData();
        
        const ticket = data.tickets.find(t => 
            t.id.toLowerCase() === ticketId.toLowerCase()
        );
        
        if (!ticket) {
            throw new Error('Ticket not found');
        }

        return ticket;
    }

    // Search tickets by partial ID
    async searchTickets(query) {
        await this.delay();
        const data = this.getTicketData();
        
        if (!query || query.length < 2) {
            return [];
        }

        const results = data.tickets.filter(ticket => 
            ticket.id.toLowerCase().includes(query.toLowerCase()) ||
            ticket.customer_name.toLowerCase().includes(query.toLowerCase()) ||
            ticket.contact.includes(query)
        );

        return results.slice(0, 10); // Limit to 10 results
    }

    // Create new ticket
    async createTicket(ticketData) {
        await this.delay();
        const data = this.getTicketData();

        const newTicket = {
            id: `TKT${String(data.next_ticket_id).padStart(3, '0')}`,
            customer_name: ticketData.customer_name,
            contact: ticketData.contact,
            email: ticketData.email || '',
            adults: ticketData.adults || 1,
            children: ticketData.children || 0,
            total_persons: (ticketData.adults || 1) + (ticketData.children || 0),
            total_amount: ((ticketData.adults || 1) + (ticketData.children || 0)) * 3500,
            booking_date: ticketData.booking_date || new Date().toISOString().split('T')[0],
            status: 'confirmed',
            created_at: new Date().toISOString()
        };

        data.tickets.push(newTicket);
        data.next_ticket_id += 1;

        this.updateTicketData(data);
        return newTicket;
    }

    // Update ticket status
    async updateTicketStatus(ticketId, status) {
        await this.delay();
        const data = this.getTicketData();
        
        const ticketIndex = data.tickets.findIndex(t => t.id === ticketId);
        if (ticketIndex === -1) {
            throw new Error('Ticket not found');
        }

        data.tickets[ticketIndex].status = status;
        data.tickets[ticketIndex].updated_at = new Date().toISOString();

        this.updateTicketData(data);
        return data.tickets[ticketIndex];
    }

    // Get all tickets
    async getAllTickets() {
        await this.delay();
        const data = this.getTicketData();
        return data.tickets.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }

    // Get tickets by date
    async getTicketsByDate(date) {
        await this.delay();
        const data = this.getTicketData();
        return data.tickets.filter(ticket => ticket.booking_date === date);
    }

    // Get ticket statistics
    async getTicketStats() {
        await this.delay();
        const data = this.getTicketData();
        const tickets = data.tickets;

        const stats = {
            total_tickets: tickets.length,
            confirmed_tickets: tickets.filter(t => t.status === 'confirmed').length,
            used_tickets: tickets.filter(t => t.status === 'used').length,
            cancelled_tickets: tickets.filter(t => t.status === 'cancelled').length,
            total_persons: tickets.reduce((sum, ticket) => sum + ticket.total_persons, 0),
            total_revenue: tickets.filter(t => t.status !== 'cancelled').reduce((sum, ticket) => sum + ticket.total_amount, 0)
        };

        return stats;
    }

    // Validate ticket for slot assignment
    async validateTicketForSlot(ticketId) {
        const ticket = await this.getTicketById(ticketId);

        if (ticket.status === 'used') {
            throw new Error('Ticket has already been used');
        }

        if (ticket.status === 'cancelled') {
            throw new Error('Ticket has been cancelled');
        }

        return ticket;
    }

    // Mark ticket as used
    async markTicketAsUsed(ticketId, slotId) {
        await this.delay();
        const data = this.getTicketData();
        
        const ticketIndex = data.tickets.findIndex(t => t.id === ticketId);
        if (ticketIndex === -1) {
            throw new Error('Ticket not found');
        }

        data.tickets[ticketIndex].status = 'used';
        data.tickets[ticketIndex].used_at = new Date().toISOString();
        data.tickets[ticketIndex].slot_id = slotId;

        this.updateTicketData(data);
        return data.tickets[ticketIndex];
    }
}

const ticketService = new TicketService();
export default ticketService;
