import React, { useState, useEffect } from 'react';
import {
    ShieldCheckIcon,
    ShieldExclamationIcon,
    ClockIcon,
    UserIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import permissionService from '../../services/permissionService';
import toast from 'react-hot-toast';

const PermissionManagement = () => {
    const { user } = useAuth();
    const [permissions, setPermissions] = useState(null);
    const [availablePermissions, setAvailablePermissions] = useState([]);
    const [permissionHistory, setPermissionHistory] = useState([]);
    const [loading, setLoading] = useState(true);
    const [actionLoading, setActionLoading] = useState(null);

    useEffect(() => {
        fetchPermissionData();
    }, []);

    const fetchPermissionData = async () => {
        try {
            setLoading(true);
            const [managerPerms, history] = await Promise.all([
                permissionService.getManagerPermissions(),
                permissionService.getPermissionHistory(5)
            ]);
            
            setPermissions(managerPerms);
            setAvailablePermissions(permissionService.getAvailablePermissions());
            setPermissionHistory(history);
        } catch (error) {
            toast.error('Failed to fetch permission data');
        } finally {
            setLoading(false);
        }
    };

    const handlePermissionToggle = async (permissionKey, currentStatus) => {
        try {
            setActionLoading(permissionKey);
            
            let result;
            if (currentStatus) {
                // Revoke permission
                result = await permissionService.revokePermission(
                    permissionKey, 
                    user.name,
                    'Permission revoked by admin'
                );
                toast.success(result.message);
            } else {
                // Grant permission
                result = await permissionService.grantPermission(
                    permissionKey, 
                    user.name,
                    'Permission granted by admin'
                );
                toast.success(result.message);
            }
            
            // Refresh data
            await fetchPermissionData();
        } catch (error) {
            toast.error('Failed to update permission');
        } finally {
            setActionLoading(null);
        }
    };

    const getRiskLevelColor = (riskLevel) => {
        switch (riskLevel) {
            case 'high':
                return 'bg-red-100 text-red-800 border-red-200';
            case 'medium':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'low':
                return 'bg-green-100 text-green-800 border-green-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getStatusIcon = (hasPermission) => {
        return hasPermission ? (
            <CheckCircleIcon className="h-5 w-5 text-green-600" />
        ) : (
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
        );
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center">
                    <ShieldCheckIcon className="h-8 w-8 text-blue-600 mr-3" />
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">Permission Management</h3>
                        <p className="text-sm text-gray-600 mt-1">
                            Control what actions managers can perform in the system
                        </p>
                    </div>
                </div>
            </div>

            {/* Permission Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {availablePermissions.map((permission) => {
                    const hasPermission = permissions[permission.key];
                    const isLoading = actionLoading === permission.key;
                    
                    return (
                        <div key={permission.key} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div className="flex items-start justify-between">
                                <div className="flex-1">
                                    <div className="flex items-center mb-2">
                                        {getStatusIcon(hasPermission)}
                                        <h4 className="text-lg font-medium text-gray-900 ml-2">
                                            {permission.name}
                                        </h4>
                                    </div>
                                    
                                    <p className="text-sm text-gray-600 mb-3">
                                        {permission.description}
                                    </p>
                                    
                                    <div className="flex items-center space-x-3 mb-4">
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {permission.category}
                                        </span>
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRiskLevelColor(permission.risk_level)}`}>
                                            {permission.risk_level.toUpperCase()} RISK
                                        </span>
                                    </div>
                                </div>
                                
                                <div className="ml-4">
                                    <button
                                        onClick={() => handlePermissionToggle(permission.key, hasPermission)}
                                        disabled={isLoading}
                                        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${
                                            hasPermission ? 'bg-teal-600' : 'bg-gray-200'
                                        } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                    >
                                        <span
                                            className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                                                hasPermission ? 'translate-x-5' : 'translate-x-0'
                                            }`}
                                        />
                                        {isLoading && (
                                            <div className="absolute inset-0 flex items-center justify-center">
                                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-teal-600"></div>
                                            </div>
                                        )}
                                    </button>
                                </div>
                            </div>
                            
                            {hasPermission && permissions.granted_at && (
                                <div className="mt-4 p-3 bg-green-50 rounded-md border border-green-200">
                                    <div className="text-xs text-green-700">
                                        <div className="font-medium">Granted by: {permissions.granted_by}</div>
                                        <div>Date: {new Date(permissions.granted_at).toLocaleString()}</div>
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>

            {/* Permission History */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-gray-500 mr-2" />
                        <h3 className="text-lg font-medium text-gray-900">Recent Permission Changes</h3>
                    </div>
                </div>
                
                <div className="p-6">
                    {permissionHistory.length === 0 ? (
                        <div className="text-center py-8">
                            <ShieldExclamationIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No permission changes</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Permission changes will appear here when they occur.
                            </p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {permissionHistory.map((entry) => (
                                <div key={entry.id} className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                                    <div className="flex-shrink-0">
                                        {entry.status === 'granted' ? (
                                            <CheckCircleIcon className="h-6 w-6 text-green-600" />
                                        ) : (
                                            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center space-x-2">
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                entry.status === 'granted' 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {entry.status.toUpperCase()}
                                            </span>
                                            <span className="text-sm font-medium text-gray-900">
                                                {entry.permission.replace('_', ' ').toUpperCase()}
                                            </span>
                                        </div>
                                        <div className="mt-1 text-sm text-gray-600">
                                            {entry.reason}
                                        </div>
                                        <div className="mt-1 flex items-center text-xs text-gray-500">
                                            <UserIcon className="h-3 w-3 mr-1" />
                                            {entry.granted_by} • {new Date(entry.timestamp).toLocaleString()}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Warning Notice */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                    <div className="text-sm text-yellow-700">
                        <p className="font-medium">Important Security Notice</p>
                        <p className="mt-1">
                            Granting payment release permission allows managers to process financial transactions. 
                            Only grant this permission to trusted managers who require this access for their role.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PermissionManagement;
