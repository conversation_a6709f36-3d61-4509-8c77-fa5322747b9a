-- =====================================================
-- Activity CRM Database Structure
-- Complete database for Activity CRM System
-- Compatible with MySQL/MariaDB for XAMPP
-- =====================================================

-- Create Database
CREATE DATABASE IF NOT EXISTS `activity_crm` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `activity_crm`;

-- =====================================================
-- 1. USERS & AUTHENTICATION
-- =====================================================

-- Users table for authentication
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','manager','elephanta_staff','trekking_staff','boat_boy','agent') NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `employee_id` varchar(50) UNIQUE,
  `phone` varchar(20),
  `hire_date` date,
  `commission_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 2. ACTIVITIES & SERVICES
-- =====================================================

-- Activities table
CREATE TABLE `activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `child_price` decimal(10,2) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `gst_rate` decimal(5,2) DEFAULT 18.00,
  `agent_commission` decimal(10,2) DEFAULT 0.00,
  `staff_commission` decimal(10,2) DEFAULT 0.00,
  `boat_boy_commission` decimal(10,2) DEFAULT 0.00,
  `child_agent_commission` decimal(10,2) DEFAULT 0.00,
  `child_staff_commission` decimal(10,2) DEFAULT 0.00,
  `child_boat_boy_commission` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 3. AGENCIES & AGENTS
-- =====================================================

-- Agencies table
CREATE TABLE `agencies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `contact_person` varchar(255),
  `email` varchar(255),
  `phone` varchar(20),
  `address` text,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Agents table
CREATE TABLE `agents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11),
  `agency_id` int(11),
  `name` varchar(255) NOT NULL,
  `email` varchar(255),
  `phone` varchar(20),
  `employee_id` varchar(50),
  `status` enum('active','inactive') DEFAULT 'active',
  `wallet_balance` decimal(10,2) DEFAULT 0.00,
  `total_earned` decimal(10,2) DEFAULT 0.00,
  `total_paid` decimal(10,2) DEFAULT 0.00,
  `last_activity` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`agency_id`) REFERENCES `agencies`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 4. STAFF & PERSONNEL
-- =====================================================

-- Staff table
CREATE TABLE `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11),
  `name` varchar(255) NOT NULL,
  `email` varchar(255),
  `phone` varchar(20),
  `employee_id` varchar(50) UNIQUE,
  `role` enum('elephanta_staff','trekking_staff','manager') NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `hire_date` date,
  `wallet_balance` decimal(10,2) DEFAULT 0.00,
  `total_earned` decimal(10,2) DEFAULT 0.00,
  `total_paid` decimal(10,2) DEFAULT 0.00,
  `last_activity` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Boat Boys table
CREATE TABLE `boat_boys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11),
  `name` varchar(255) NOT NULL,
  `email` varchar(255),
  `phone` varchar(20),
  `employee_id` varchar(50) UNIQUE,
  `status` enum('active','inactive') DEFAULT 'active',
  `hire_date` date,
  `wallet_balance` decimal(10,2) DEFAULT 0.00,
  `total_earned` decimal(10,2) DEFAULT 0.00,
  `total_paid` decimal(10,2) DEFAULT 0.00,
  `last_activity` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 5. SLOTS & BOOKINGS
-- =====================================================

-- Slots table
CREATE TABLE `slots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL,
  `activity_name` varchar(255) NOT NULL,
  `date` date NOT NULL,
  `time` time NOT NULL,
  `capacity` int(11) DEFAULT 12,
  `booked_count` int(11) DEFAULT 0,
  `available_count` int(11) DEFAULT 12,
  `status` enum('active','cancelled','completed') DEFAULT 'active',
  `total_amount` decimal(10,2) DEFAULT 0.00,
  `collected_amount` decimal(10,2) DEFAULT 0.00,
  `commission_amount` decimal(10,2) DEFAULT 0.00,
  `created_by` int(11),
  `created_by_name` varchar(255),
  `auto_created` boolean DEFAULT false,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`activity_id`) REFERENCES `activities`(`id`),
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Participants table
CREATE TABLE `participants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `slot_id` int(11) NOT NULL,
  `ticket_id` varchar(50),
  `mode_of_journey` enum('via_boat','via_trekking') DEFAULT 'via_boat',
  `boat_name` varchar(255),
  `guide_name` varchar(255),
  `name` varchar(255) NOT NULL,
  `contact` varchar(20),
  `email` varchar(255),
  `age` int(11),
  `adults` int(11) DEFAULT 1,
  `children` int(11) DEFAULT 0,
  `group_size` int(11) DEFAULT 1,
  `amount_per_person` decimal(10,2) DEFAULT 3500.00,
  `total_amount` decimal(10,2) DEFAULT 3500.00,
  `payment_status` enum('pending','collected','refunded') DEFAULT 'pending',
  `status` enum('confirmed','arrived','departed','completed','cancelled') DEFAULT 'confirmed',
  `special_requirements` text,
  `arrival_time` time NULL,
  `departure_time` time NULL,
  `completed_time` time NULL,
  `booking_date` date,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`slot_id`) REFERENCES `slots`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 6. BOOKINGS
-- =====================================================

-- Bookings table
CREATE TABLE `bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_number` varchar(50) UNIQUE NOT NULL,
  `guest_name` varchar(255) NOT NULL,
  `contact_number` varchar(20) NOT NULL,
  `email` varchar(255),
  `booking_date` date NOT NULL,
  `activity_id` int(11) NOT NULL,
  `activity_name` varchar(255) NOT NULL,
  `activity_price` decimal(10,2) NOT NULL,
  `adults` int(11) DEFAULT 1,
  `children` int(11) DEFAULT 0,
  `total_persons` int(11) DEFAULT 1,
  `total_amount` decimal(10,2) NOT NULL,
  `booking_type` enum('direct','agency') DEFAULT 'direct',
  `agency_id` int(11) NULL,
  `agency_name` varchar(255) NULL,
  `agent_id` int(11) NULL,
  `agent_name` varchar(255) NULL,
  `staff_id` int(11) NULL,
  `staff_name` varchar(255) NULL,
  `boat_boy_id` int(11) NULL,
  `boat_boy_name` varchar(255) NULL,
  `agent_commission` decimal(10,2) DEFAULT 0.00,
  `staff_commission` decimal(10,2) DEFAULT 0.00,
  `boat_boy_commission` decimal(10,2) DEFAULT 0.00,
  `total_commission` decimal(10,2) DEFAULT 0.00,
  `gst_amount` decimal(10,2) DEFAULT 0.00,
  `admin_share` decimal(10,2) DEFAULT 0.00,
  `status` enum('confirmed','cancelled','completed','refunded') DEFAULT 'confirmed',
  `created_by` int(11),
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`activity_id`) REFERENCES `activities`(`id`),
  FOREIGN KEY (`agency_id`) REFERENCES `agencies`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`boat_boy_id`) REFERENCES `boat_boys`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 7. TICKETS
-- =====================================================

-- Tickets table
CREATE TABLE `tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` varchar(50) UNIQUE NOT NULL,
  `customer_name` varchar(255) NOT NULL,
  `contact` varchar(20) NOT NULL,
  `email` varchar(255),
  `adults` int(11) DEFAULT 1,
  `children` int(11) DEFAULT 0,
  `total_persons` int(11) DEFAULT 1,
  `booking_date` date NOT NULL,
  `activity_id` int(11) NOT NULL,
  `activity_name` varchar(255) NOT NULL,
  `amount_per_person` decimal(10,2) DEFAULT 3500.00,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('active','used','expired','cancelled') DEFAULT 'active',
  `used_at` timestamp NULL,
  `used_by` int(11) NULL,
  `slot_id` int(11) NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`activity_id`) REFERENCES `activities`(`id`),
  FOREIGN KEY (`used_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`slot_id`) REFERENCES `slots`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 8. WALLET & PAYMENT SYSTEM
-- =====================================================

-- Wallet Transactions table
CREATE TABLE `wallet_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `user_type` enum('staff','boat_boy','agent') NOT NULL,
  `transaction_type` enum('commission_earned','payment_received','payment_released','refund','adjustment') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `balance_before` decimal(10,2) NOT NULL,
  `balance_after` decimal(10,2) NOT NULL,
  `booking_id` int(11) NULL,
  `booking_number` varchar(50) NULL,
  `slot_id` int(11) NULL,
  `participant_id` int(11) NULL,
  `activity_name` varchar(255) NULL,
  `guest_name` varchar(255) NULL,
  `description` text,
  `status` enum('pending','completed','failed','cancelled') DEFAULT 'completed',
  `processed_by` int(11) NULL,
  `processed_at` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`booking_id`) REFERENCES `bookings`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`slot_id`) REFERENCES `slots`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`participant_id`) REFERENCES `participants`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`processed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Payment Releases table
CREATE TABLE `payment_releases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `release_number` varchar(50) UNIQUE NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` enum('staff','boat_boy','agent') NOT NULL,
  `user_name` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `transaction_count` int(11) DEFAULT 0,
  `release_mode` enum('cash','bank_transfer','upi','cheque') DEFAULT 'cash',
  `reference_number` varchar(100) NULL,
  `notes` text,
  `status` enum('pending','completed','cancelled') DEFAULT 'completed',
  `released_by` int(11) NOT NULL,
  `released_by_name` varchar(255) NOT NULL,
  `released_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`released_by`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 9. PERMISSIONS & SETTINGS
-- =====================================================

-- Permissions table
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role` enum('manager','elephanta_staff','trekking_staff') NOT NULL,
  `permission_key` varchar(100) NOT NULL,
  `permission_name` varchar(255) NOT NULL,
  `granted` boolean DEFAULT false,
  `granted_by` int(11) NULL,
  `granted_at` timestamp NULL,
  `revoked_by` int(11) NULL,
  `revoked_at` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_permission` (`role`, `permission_key`),
  FOREIGN KEY (`granted_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`revoked_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Permission History table
CREATE TABLE `permission_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` enum('granted','revoked','system_initialization') NOT NULL,
  `permission_key` varchar(100) NOT NULL,
  `permission_name` varchar(255) NOT NULL,
  `granted_to_role` enum('manager','elephanta_staff','trekking_staff') NOT NULL,
  `granted_by` int(11) NULL,
  `granted_by_name` varchar(255) NULL,
  `reason` text,
  `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`granted_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- 10. SAMPLE DATA INSERTION
-- =====================================================

-- Insert default admin user
INSERT INTO `users` (`name`, `email`, `password`, `role`, `status`, `employee_id`, `phone`, `hire_date`) VALUES
('Admin User', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', 'ADM001', '+91 9876543210', '2024-01-01'),
('Manager User', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'manager', 'active', 'MGR001', '+91 9876543211', '2024-01-01'),
('Elephanta Staff', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'elephanta_staff', 'active', 'ELE001', '+91 9876543212', '2024-01-01'),
('Trekking Staff', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'trekking_staff', 'active', 'TRK001', '+91 9876543213', '2024-01-01'),
('Boat Boy 1', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'boat_boy', 'active', 'BOT001', '+91 9876543214', '2024-01-01'),
('Agent User', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'agent', 'active', 'AGT001', '+91 9876543215', '2024-01-01');

-- Insert default activities
INSERT INTO `activities` (`name`, `description`, `price`, `child_price`, `status`, `gst_rate`, `agent_commission`, `staff_commission`, `boat_boy_commission`, `child_agent_commission`, `child_staff_commission`, `child_boat_boy_commission`) VALUES
('Semi-Submarine Experience', 'Underwater viewing experience with semi-submarine boat', 3500.00, 2500.00, 'active', 18.00, 200.00, 150.00, 100.00, 150.00, 100.00, 75.00),
('Trekking + Semi-Submarine', 'Combined trekking and semi-submarine experience', 4500.00, 3500.00, 'active', 18.00, 300.00, 200.00, 150.00, 200.00, 150.00, 100.00),
('Island Tour', 'Complete Elephanta Island tour with guide', 2500.00, 1500.00, 'active', 18.00, 150.00, 100.00, 75.00, 100.00, 75.00, 50.00),
('Photography Package', 'Professional photography during activities', 1500.00, 1000.00, 'active', 18.00, 100.00, 75.00, 50.00, 75.00, 50.00, 25.00);

-- Insert default agencies
INSERT INTO `agencies` (`name`, `contact_person`, `email`, `phone`, `address`, `status`) VALUES
('Mumbai Tours & Travels', 'Rajesh Kumar', '<EMAIL>', '+91 9876543220', 'Mumbai, Maharashtra', 'active'),
('Gateway Adventures', 'Priya Sharma', '<EMAIL>', '+91 9876543221', 'Mumbai, Maharashtra', 'active'),
('Island Explorers', 'Amit Patel', '<EMAIL>', '+91 9876543222', 'Mumbai, Maharashtra', 'active');

-- Insert staff records
INSERT INTO `staff` (`user_id`, `name`, `email`, `phone`, `employee_id`, `role`, `status`, `hire_date`, `wallet_balance`, `total_earned`, `total_paid`) VALUES
(2, 'Manager User', '<EMAIL>', '+91 9876543211', 'MGR001', 'manager', 'active', '2024-01-01', 0.00, 0.00, 0.00),
(3, 'Elephanta Staff', '<EMAIL>', '+91 9876543212', 'ELE001', 'elephanta_staff', 'active', '2024-01-01', 0.00, 0.00, 0.00),
(4, 'Trekking Staff', '<EMAIL>', '+91 9876543213', 'TRK001', 'trekking_staff', 'active', '2024-01-01', 0.00, 0.00, 0.00);

-- Insert boat boys
INSERT INTO `boat_boys` (`user_id`, `name`, `email`, `phone`, `employee_id`, `status`, `hire_date`, `wallet_balance`, `total_earned`, `total_paid`) VALUES
(5, 'Boat Boy 1', '<EMAIL>', '+91 9876543214', 'BOT001', 'active', '2024-01-01', 0.00, 0.00, 0.00);

-- Insert agents
INSERT INTO `agents` (`user_id`, `agency_id`, `name`, `email`, `phone`, `employee_id`, `status`, `wallet_balance`, `total_earned`, `total_paid`) VALUES
(6, 1, 'Agent User', '<EMAIL>', '+91 9876543215', 'AGT001', 'active', 0.00, 0.00, 0.00);

-- Insert sample slots for today
INSERT INTO `slots` (`activity_id`, `activity_name`, `date`, `time`, `capacity`, `booked_count`, `available_count`, `status`, `total_amount`, `collected_amount`, `commission_amount`, `created_by`, `created_by_name`, `auto_created`) VALUES
(1, 'Semi-Submarine Experience', CURDATE(), '09:00:00', 12, 0, 12, 'active', 0.00, 0.00, 0.00, 3, 'Elephanta Staff', false),
(1, 'Semi-Submarine Experience', CURDATE(), '11:00:00', 12, 0, 12, 'active', 0.00, 0.00, 0.00, 3, 'Elephanta Staff', false),
(1, 'Semi-Submarine Experience', CURDATE(), '13:00:00', 12, 0, 12, 'active', 0.00, 0.00, 0.00, 3, 'Elephanta Staff', false),
(1, 'Semi-Submarine Experience', CURDATE(), '15:00:00', 12, 0, 12, 'active', 0.00, 0.00, 0.00, 3, 'Elephanta Staff', false),
(2, 'Trekking + Semi-Submarine', CURDATE(), '10:00:00', 8, 0, 8, 'active', 0.00, 0.00, 0.00, 4, 'Trekking Staff', false),
(2, 'Trekking + Semi-Submarine', CURDATE(), '14:00:00', 8, 0, 8, 'active', 0.00, 0.00, 0.00, 4, 'Trekking Staff', false);

-- Insert default permissions for staff roles
INSERT INTO `permissions` (`role`, `permission_key`, `permission_name`, `granted`, `granted_by`, `granted_at`) VALUES
('manager', 'create_slots', 'Create Activity Slots', true, 1, NOW()),
('manager', 'manage_participants', 'Manage Participants', true, 1, NOW()),
('manager', 'collect_payments', 'Collect Payments', true, 1, NOW()),
('manager', 'view_reports', 'View Reports', true, 1, NOW()),
('manager', 'manage_staff', 'Manage Staff', true, 1, NOW()),
('manager', 'release_payments', 'Release Payments', true, 1, NOW()),
('elephanta_staff', 'create_slots', 'Create Activity Slots', true, 1, NOW()),
('elephanta_staff', 'manage_participants', 'Manage Participants', true, 1, NOW()),
('elephanta_staff', 'collect_payments', 'Collect Payments', true, 1, NOW()),
('elephanta_staff', 'view_reports', 'View Reports', false, NULL, NULL),
('elephanta_staff', 'manage_staff', 'Manage Staff', false, NULL, NULL),
('elephanta_staff', 'release_payments', 'Release Payments', false, NULL, NULL),
('trekking_staff', 'create_slots', 'Create Activity Slots', true, 1, NOW()),
('trekking_staff', 'manage_participants', 'Manage Participants', true, 1, NOW()),
('trekking_staff', 'collect_payments', 'Collect Payments', true, 1, NOW()),
('trekking_staff', 'view_reports', 'View Reports', false, NULL, NULL),
('trekking_staff', 'manage_staff', 'Manage Staff', false, NULL, NULL),
('trekking_staff', 'release_payments', 'Release Payments', false, NULL, NULL);

-- Insert permission history
INSERT INTO `permission_history` (`action`, `permission_key`, `permission_name`, `granted_to_role`, `granted_by`, `granted_by_name`, `reason`) VALUES
('system_initialization', 'create_slots', 'Create Activity Slots', 'manager', 1, 'Admin User', 'System initialization'),
('system_initialization', 'manage_participants', 'Manage Participants', 'manager', 1, 'Admin User', 'System initialization'),
('system_initialization', 'collect_payments', 'Collect Payments', 'manager', 1, 'Admin User', 'System initialization'),
('system_initialization', 'view_reports', 'View Reports', 'manager', 1, 'Admin User', 'System initialization'),
('system_initialization', 'manage_staff', 'Manage Staff', 'manager', 1, 'Admin User', 'System initialization'),
('system_initialization', 'release_payments', 'Release Payments', 'manager', 1, 'Admin User', 'System initialization'),
('system_initialization', 'create_slots', 'Create Activity Slots', 'elephanta_staff', 1, 'Admin User', 'System initialization'),
('system_initialization', 'manage_participants', 'Manage Participants', 'elephanta_staff', 1, 'Admin User', 'System initialization'),
('system_initialization', 'collect_payments', 'Collect Payments', 'elephanta_staff', 1, 'Admin User', 'System initialization'),
('system_initialization', 'create_slots', 'Create Activity Slots', 'trekking_staff', 1, 'Admin User', 'System initialization'),
('system_initialization', 'manage_participants', 'Manage Participants', 'trekking_staff', 1, 'Admin User', 'System initialization'),
('system_initialization', 'collect_payments', 'Collect Payments', 'trekking_staff', 1, 'Admin User', 'System initialization');

-- =====================================================
-- 11. INDEXES FOR PERFORMANCE
-- =====================================================

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_slots_date ON slots(date);
CREATE INDEX idx_slots_status ON slots(status);
CREATE INDEX idx_participants_slot_id ON participants(slot_id);
CREATE INDEX idx_participants_status ON participants(status);
CREATE INDEX idx_bookings_date ON bookings(booking_date);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_wallet_transactions_user_id ON wallet_transactions(user_id);
CREATE INDEX idx_wallet_transactions_date ON wallet_transactions(created_at);

-- =====================================================
-- 12. VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for active slots with participant count
CREATE VIEW `active_slots_view` AS
SELECT
    s.id,
    s.activity_id,
    s.activity_name,
    s.date,
    s.time,
    s.capacity,
    s.booked_count,
    s.available_count,
    s.status,
    s.total_amount,
    s.collected_amount,
    s.created_by_name,
    COUNT(p.id) as actual_participants
FROM slots s
LEFT JOIN participants p ON s.id = p.slot_id AND p.status != 'cancelled'
WHERE s.status = 'active'
GROUP BY s.id;

-- View for staff wallet summary
CREATE VIEW `staff_wallet_summary` AS
SELECT
    u.id as user_id,
    u.name,
    u.role,
    u.email,
    COALESCE(s.wallet_balance, bb.wallet_balance, a.wallet_balance, 0) as wallet_balance,
    COALESCE(s.total_earned, bb.total_earned, a.total_earned, 0) as total_earned,
    COALESCE(s.total_paid, bb.total_paid, a.total_paid, 0) as total_paid
FROM users u
LEFT JOIN staff s ON u.id = s.user_id
LEFT JOIN boat_boys bb ON u.id = bb.user_id
LEFT JOIN agents a ON u.id = a.user_id
WHERE u.role IN ('manager', 'elephanta_staff', 'trekking_staff', 'boat_boy', 'agent')
AND u.status = 'active';

-- =====================================================
-- DATABASE SETUP COMPLETE
-- =====================================================

-- Note: Default password for all users is 'password'
-- Password hash: $2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

-- Login Credentials:
-- Admin: <EMAIL> / password
-- Manager: <EMAIL> / password
-- Elephanta Staff: <EMAIL> / password
-- Trekking Staff: <EMAIL> / password
-- Boat Boy: <EMAIL> / password
-- Agent: <EMAIL> / password
