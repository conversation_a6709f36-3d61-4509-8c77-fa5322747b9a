import React, { useState } from 'react';
import { XMarkIcon, CurrencyRupeeIcon, CreditCardIcon, BanknotesIcon } from '@heroicons/react/24/outline';

const TransferModal = ({ isOpen, onClose, onConfirm, transferAmount, commissionAmount, bookingCount }) => {
    const [transferMode, setTransferMode] = useState('cash');
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        
        try {
            await onConfirm(transferMode);
            onClose();
        } catch (error) {
            // Error handling is done in parent component
        } finally {
            setLoading(false);
        }
    };

    const resetForm = () => {
        setTransferMode('cash');
        setLoading(false);
    };

    const handleClose = () => {
        resetForm();
        onClose();
    };

    if (!isOpen) return null;

    const transferModes = [
        {
            id: 'cash',
            name: 'Cash Payment',
            description: 'Physical cash handover to admin',
            icon: BanknotesIcon,
            color: 'green'
        },
        {
            id: 'gpay',
            name: 'Google Pay',
            description: 'Transfer via Google Pay UPI',
            icon: CreditCardIcon,
            color: 'blue'
        },
        {
            id: 'phonepe',
            name: 'PhonePe',
            description: 'Transfer via PhonePe UPI',
            icon: CreditCardIcon,
            color: 'purple'
        },
        {
            id: 'paytm',
            name: 'Paytm',
            description: 'Transfer via Paytm wallet/UPI',
            icon: CreditCardIcon,
            color: 'indigo'
        },
        {
            id: 'bank_transfer',
            name: 'Bank Transfer',
            description: 'Direct bank account transfer',
            icon: CreditCardIcon,
            color: 'gray'
        },
        {
            id: 'other',
            name: 'Other',
            description: 'Other payment method',
            icon: CurrencyRupeeIcon,
            color: 'orange'
        }
    ];

    const getColorClasses = (color, isSelected) => {
        const colors = {
            green: isSelected ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-green-300',
            blue: isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300',
            purple: isSelected ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300',
            indigo: isSelected ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-indigo-300',
            gray: isSelected ? 'border-gray-500 bg-gray-50' : 'border-gray-200 hover:border-gray-300',
            orange: isSelected ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-orange-300'
        };
        return colors[color] || colors.gray;
    };

    const getIconColor = (color) => {
        const colors = {
            green: 'text-green-600',
            blue: 'text-blue-600',
            purple: 'text-purple-600',
            indigo: 'text-indigo-600',
            gray: 'text-gray-600',
            orange: 'text-orange-600'
        };
        return colors[color] || colors.gray;
    };

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        Transfer Money to Admin
                    </h3>
                    <button
                        onClick={handleClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                {/* Transfer Summary */}
                <div className="bg-blue-50 p-4 rounded-lg mb-6">
                    <h4 className="text-sm font-medium text-blue-900 mb-3">Transfer Summary</h4>
                    <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                            <span className="text-blue-800">Total Amount:</span>
                            <span className="font-semibold text-blue-900">₹{transferAmount.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-blue-800">Number of Bookings:</span>
                            <span className="font-semibold text-blue-900">{bookingCount}</span>
                        </div>
                        <div className="flex justify-between border-t border-blue-200 pt-2">
                            <span className="text-blue-800">Your Commission:</span>
                            <span className="font-bold text-green-600">₹{commissionAmount.toLocaleString()}</span>
                        </div>
                        <div className="text-xs text-blue-700 mt-2">
                            Commission: ₹{Math.round(commissionAmount / bookingCount)} per booking
                        </div>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                            Select Transfer Mode *
                        </label>
                        <div className="grid grid-cols-1 gap-3">
                            {transferModes.map((mode) => {
                                const IconComponent = mode.icon;
                                const isSelected = transferMode === mode.id;
                                
                                return (
                                    <label
                                        key={mode.id}
                                        className={`relative flex items-center p-3 border rounded-lg cursor-pointer transition-all ${getColorClasses(mode.color, isSelected)}`}
                                    >
                                        <input
                                            type="radio"
                                            name="transferMode"
                                            value={mode.id}
                                            checked={isSelected}
                                            onChange={(e) => setTransferMode(e.target.value)}
                                            className="sr-only"
                                        />
                                        <div className={`flex-shrink-0 p-2 rounded-lg ${isSelected ? 'bg-white' : 'bg-gray-100'}`}>
                                            <IconComponent className={`h-5 w-5 ${getIconColor(mode.color)}`} />
                                        </div>
                                        <div className="ml-3 flex-1">
                                            <div className="text-sm font-medium text-gray-900">
                                                {mode.name}
                                            </div>
                                            <div className="text-xs text-gray-600">
                                                {mode.description}
                                            </div>
                                        </div>
                                        {isSelected && (
                                            <div className="flex-shrink-0">
                                                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                                            </div>
                                        )}
                                    </label>
                                );
                            })}
                        </div>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                        <div className="text-sm text-yellow-800">
                            <strong>Note:</strong> After transfer, the admin will receive ₹{transferAmount.toLocaleString()} 
                            and your commission of ₹{commissionAmount.toLocaleString()} will be credited to your account.
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                        <button
                            type="button"
                            onClick={handleClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
                        >
                            {loading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <CurrencyRupeeIcon className="h-4 w-4" />
                                    Confirm Transfer
                                </>
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default TransferModal;
