import React, { useState, useEffect } from 'react';
import {
    ArrowRightOnRectangleIcon,
    ArrowLeftOnRectangleIcon,
    CheckCircleIcon,
    CalendarIcon,
    ClockIcon,
    UsersIcon,
    PhoneIcon,
    EnvelopeIcon
} from '@heroicons/react/24/outline';
import slotService from '../../services/slotService';
import toast from 'react-hot-toast';

const PassengerManagement = () => {
    const [activeTab, setActiveTab] = useState('arrival');
    const [passengers, setPassengers] = useState({
        arrival: [],
        departure: [],
        completed: []
    });
    const [loading, setLoading] = useState(true);
    const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

    const tabs = [
        {
            id: 'arrival',
            name: 'Arrival Passengers',
            icon: ArrowRightOnRectangleIcon,
            color: 'text-green-600',
            bgColor: 'bg-green-100',
            description: 'Passengers arriving today'
        },
        {
            id: 'departure',
            name: 'Departure Passengers',
            icon: ArrowLeftOnRectangleIcon,
            color: 'text-blue-600',
            bgColor: 'bg-blue-100',
            description: 'Passengers departing today'
        },
        {
            id: 'completed',
            name: 'Completed Trips',
            icon: CheckCircleIcon,
            color: 'text-purple-600',
            bgColor: 'bg-purple-100',
            description: 'Passengers who completed their trip'
        }
    ];

    useEffect(() => {
        fetchPassengers();
    }, [selectedDate]); // eslint-disable-line react-hooks/exhaustive-deps

    const fetchPassengers = async () => {
        try {
            setLoading(true);
            const data = await slotService.getPassengersByStatus(selectedDate);
            setPassengers(data);
        } catch (error) {
            console.error('Error fetching passengers:', error);
            toast.error('Failed to fetch passenger data');
            // Mock data for development
            setPassengers({
                arrival: [
                    {
                        id: 1,
                        name: 'John Smith',
                        phone: '+91 9876543210',
                        email: '<EMAIL>',
                        slot_time: '09:00',
                        activity_name: 'Semi-Submarine Experience',
                        group_size: 2,
                        status: 'confirmed',
                        arrival_time: null
                    },
                    {
                        id: 2,
                        name: 'Sarah Johnson',
                        phone: '+91 9876543211',
                        email: '<EMAIL>',
                        slot_time: '10:30',
                        activity_name: 'Trekking + Semi-Submarine',
                        group_size: 4,
                        status: 'confirmed',
                        arrival_time: null
                    }
                ],
                departure: [
                    {
                        id: 3,
                        name: 'Mike Wilson',
                        phone: '+91 9876543212',
                        email: '<EMAIL>',
                        slot_time: '11:00',
                        activity_name: 'Semi-Submarine Experience',
                        group_size: 3,
                        status: 'in_progress',
                        departure_time: null
                    }
                ],
                completed: [
                    {
                        id: 4,
                        name: 'Lisa Davis',
                        phone: '+91 9876543213',
                        email: '<EMAIL>',
                        slot_time: '08:00',
                        activity_name: 'Semi-Submarine Experience',
                        group_size: 2,
                        status: 'completed',
                        completed_time: '09:30'
                    }
                ]
            });
        } finally {
            setLoading(false);
        }
    };

    const handleMarkArrival = async (passengerId) => {
        try {
            await slotService.markPassengerArrival(passengerId);
            toast.success('Passenger arrival marked successfully!');
            fetchPassengers();
        } catch (error) {
            toast.error('Failed to mark passenger arrival');
        }
    };

    const handleMarkDeparture = async (passengerId) => {
        try {
            await slotService.markPassengerDeparture(passengerId);
            toast.success('Passenger departure marked successfully!');
            fetchPassengers();
        } catch (error) {
            toast.error('Failed to mark passenger departure');
        }
    };

    const handleMarkCompleted = async (passengerId) => {
        try {
            await slotService.markTripCompleted(passengerId);
            toast.success('Trip marked as completed successfully!');
            fetchPassengers();
        } catch (error) {
            toast.error('Failed to mark trip as completed');
        }
    };

    const renderPassengerCard = (passenger, type) => {
        return (
            <div key={passenger.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                            <div className="flex-shrink-0">
                                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                    <UsersIcon className="h-5 w-5 text-gray-600" />
                                </div>
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900">{passenger.name}</h3>
                                <p className="text-sm text-gray-500">{passenger.activity_name}</p>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                            <div className="flex items-center text-sm text-gray-600">
                                <ClockIcon className="h-4 w-4 mr-2" />
                                <span>Slot Time: {passenger.slot_time}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                                <UsersIcon className="h-4 w-4 mr-2" />
                                <span>Group Size: {passenger.group_size}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                                <PhoneIcon className="h-4 w-4 mr-2" />
                                <span>{passenger.phone}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                                <EnvelopeIcon className="h-4 w-4 mr-2" />
                                <span className="truncate">{passenger.email}</span>
                            </div>
                        </div>

                        {type === 'completed' && passenger.completed_time && (
                            <div className="flex items-center text-sm text-green-600 mb-3">
                                <CheckCircleIcon className="h-4 w-4 mr-2" />
                                <span>Completed at: {passenger.completed_time}</span>
                            </div>
                        )}
                    </div>

                    <div className="flex flex-col space-y-2 ml-4">
                        {type === 'arrival' && (
                            <button
                                onClick={() => handleMarkArrival(passenger.id)}
                                className="px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
                            >
                                Mark Arrived
                            </button>
                        )}
                        {type === 'departure' && (
                            <button
                                onClick={() => handleMarkDeparture(passenger.id)}
                                className="px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                            >
                                Mark Departed
                            </button>
                        )}
                        {type === 'departure' && (
                            <button
                                onClick={() => handleMarkCompleted(passenger.id)}
                                className="px-3 py-2 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700 transition-colors"
                            >
                                Mark Completed
                            </button>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
            </div>
        );
    }

    const currentPassengers = passengers[activeTab] || [];

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Passenger Management</h2>
                    <p className="text-gray-600">Track passenger arrivals, departures, and completed trips</p>
                </div>
                <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                        <input
                            type="date"
                            value={selectedDate}
                            onChange={(e) => setSelectedDate(e.target.value)}
                            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        />
                    </div>
                </div>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                    {tabs.map((tab) => {
                        const Icon = tab.icon;
                        const isActive = activeTab === tab.id;
                        const count = passengers[tab.id]?.length || 0;
                        
                        return (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                                    isActive
                                        ? `border-teal-500 ${tab.color}`
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <Icon className={`-ml-0.5 mr-2 h-5 w-5 ${isActive ? tab.color : 'text-gray-400 group-hover:text-gray-500'}`} />
                                {tab.name}
                                <span className={`ml-2 py-0.5 px-2 rounded-full text-xs font-medium ${
                                    isActive ? `${tab.bgColor} ${tab.color}` : 'bg-gray-100 text-gray-900'
                                }`}>
                                    {count}
                                </span>
                            </button>
                        );
                    })}
                </nav>
            </div>

            {/* Tab Content */}
            <div className="space-y-4">
                {currentPassengers.length === 0 ? (
                    <div className="text-center py-12">
                        <div className="mx-auto h-12 w-12 text-gray-400">
                            {React.createElement(tabs.find(t => t.id === activeTab)?.icon, { className: "h-12 w-12" })}
                        </div>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">
                            No {tabs.find(t => t.id === activeTab)?.name.toLowerCase()}
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                            {tabs.find(t => t.id === activeTab)?.description} will appear here.
                        </p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 gap-4">
                        {currentPassengers.map(passenger => renderPassengerCard(passenger, activeTab))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default PassengerManagement;
