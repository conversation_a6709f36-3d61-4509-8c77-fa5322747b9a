import React, { useState, useEffect, useCallback } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import personnelService from '../../services/personnelService';
import toast from 'react-hot-toast';

const AgentManagement = ({ onStatsUpdate }) => {
    const [agents, setAgents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedAgent, setSelectedAgent] = useState(null);
    const [modalMode, setModalMode] = useState('create');
    const [filters, setFilters] = useState({
        status: '',
        search: ''
    });

    const fetchAgents = useCallback(async () => {
        try {
            setLoading(true);
            const response = await personnelService.getAgents(filters);
            setAgents(response.agents);
        } catch (error) {
            toast.error('Failed to fetch agents');
        } finally {
            setLoading(false);
        }
    }, [filters]);

    useEffect(() => {
        fetchAgents();
    }, [fetchAgents]);

    const handleCreateAgent = () => {
        setSelectedAgent(null);
        setModalMode('create');
        setModalOpen(true);
    };

    const handleEditAgent = (agent) => {
        setSelectedAgent(agent);
        setModalMode('edit');
        setModalOpen(true);
    };

    const handleDeleteAgent = async (agent) => {
        if (window.confirm(`Are you sure you want to delete "${agent.name}"?`)) {
            try {
                await personnelService.deleteAgent(agent.id);
                toast.success('Agent deleted successfully');
                fetchAgents();
                if (onStatsUpdate) onStatsUpdate();
            } catch (error) {
                toast.error(error.message || 'Failed to delete agent');
            }
        }
    };

    const handleSaveAgent = async (agentData) => {
        if (modalMode === 'create') {
            await personnelService.createAgent(agentData);
        } else {
            await personnelService.updateAgent(selectedAgent.id, agentData);
        }
        fetchAgents();
        if (onStatsUpdate) onStatsUpdate();
    };

    const getStatusColor = (status) => {
        return status === 'active' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800';
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    return (
        <div className="space-y-6">
            {/* Header and Actions */}
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-lg font-medium text-gray-900">Agents</h3>
                    <p className="text-sm text-gray-500">Manage agents and their commission information</p>
                </div>
                <button
                    onClick={handleCreateAgent}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Agent
                </button>
            </div>

            {/* Filters */}
            <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Search Agents
                        </label>
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                value={filters.search}
                                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                placeholder="Search by name, email, or agent code..."
                            />
                        </div>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Status Filter
                        </label>
                        <select
                            value={filters.status}
                            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                        >
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div className="flex items-end">
                        <button
                            onClick={() => setFilters({ status: '', search: '' })}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                            Clear Filters
                        </button>
                    </div>
                </div>
            </div>

            {/* Agents Table */}
            {loading ? (
                <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                </div>
            ) : agents.length === 0 ? (
                <div className="text-center py-12">
                    <div className="text-gray-500 text-lg mb-2">No agents found</div>
                    <p className="text-gray-400 mb-4">Get started by adding your first agent</p>
                    <button
                        onClick={handleCreateAgent}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700"
                    >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Agent
                    </button>
                </div>
            ) : (
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Agent Details
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Contact Info
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Wallet Summary
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {agents.map((agent) => (
                                    <tr key={agent.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    {agent.name}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    Code: {agent.agent_code}
                                                </div>
                                                <div className="text-xs text-gray-400">
                                                    Joined: {new Date(agent.hire_date).toLocaleDateString()}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="text-sm text-gray-900">{agent.email}</div>
                                            <div className="text-sm text-gray-500">{agent.phone}</div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="text-sm space-y-1">
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Balance:</span>
                                                    <span className="font-medium">{formatCurrency(agent.wallet_balance)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Earned:</span>
                                                    <span className="text-green-600">{formatCurrency(agent.total_earned)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Paid:</span>
                                                    <span className="text-blue-600">{formatCurrency(agent.total_paid)}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(agent.status)}`}>
                                                {agent.status}
                                            </span>
                                            {agent.last_activity && (
                                                <div className="text-xs text-gray-500 mt-1">
                                                    Last: {new Date(agent.last_activity).toLocaleDateString()}
                                                </div>
                                            )}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end space-x-2">
                                                <button
                                                    onClick={() => handleEditAgent(agent)}
                                                    className="text-teal-600 hover:text-teal-900 p-1"
                                                    title="Edit Agent"
                                                >
                                                    <PencilIcon className="h-4 w-4" />
                                                </button>
                                                <button
                                                    onClick={() => handleDeleteAgent(agent)}
                                                    className="text-red-600 hover:text-red-900 p-1"
                                                    title="Delete Agent"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {/* Agent Modal */}
            {modalOpen && (
                <AgentModal
                    isOpen={modalOpen}
                    onClose={() => setModalOpen(false)}
                    onSave={handleSaveAgent}
                    agent={selectedAgent}
                    mode={modalMode}
                />
            )}
        </div>
    );
};

// AgentModal Component
const AgentModal = ({ isOpen, onClose, onSave, agent, mode }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        agent_code: '',
        status: 'active',
        hire_date: ''
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (agent && mode === 'edit') {
            setFormData({
                name: agent.name || '',
                email: agent.email || '',
                phone: agent.phone || '',
                agent_code: agent.agent_code || '',
                status: agent.status || 'active',
                hire_date: agent.hire_date || ''
            });
        } else {
            setFormData({
                name: '',
                email: '',
                phone: '',
                agent_code: '',
                status: 'active',
                hire_date: new Date().toISOString().split('T')[0]
            });
        }
        setErrors({});
    }, [agent, mode, isOpen]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email is invalid';
        }

        if (!formData.phone.trim()) {
            newErrors.phone = 'Phone is required';
        }

        if (!formData.agent_code.trim()) {
            newErrors.agent_code = 'Agent code is required';
        }

        if (!formData.hire_date) {
            newErrors.hire_date = 'Hire date is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            await onSave(formData);
            toast.success(mode === 'create' ? 'Agent created successfully' : 'Agent updated successfully');
            onClose();
        } catch (error) {
            toast.error(error.message || 'Failed to save agent');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        {mode === 'create' ? 'Add Agent' : 'Edit Agent'}
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Full Name *
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.name ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="Enter full name"
                            />
                            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Agent Code *
                            </label>
                            <input
                                type="text"
                                name="agent_code"
                                value={formData.agent_code}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.agent_code ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="e.g., AGT001"
                            />
                            {errors.agent_code && <p className="text-red-500 text-xs mt-1">{errors.agent_code}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Email Address *
                            </label>
                            <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.email ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="Enter email address"
                            />
                            {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Phone Number *
                            </label>
                            <input
                                type="tel"
                                name="phone"
                                value={formData.phone}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.phone ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="+91 9876543210"
                            />
                            {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Hire Date *
                            </label>
                            <input
                                type="date"
                                name="hire_date"
                                value={formData.hire_date}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.hire_date ? 'border-red-300' : 'border-gray-300'
                                }`}
                            />
                            {errors.hire_date && <p className="text-red-500 text-xs mt-1">{errors.hire_date}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Status
                            </label>
                            <select
                                name="status"
                                value={formData.status}
                                onChange={handleChange}
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
                        >
                            {loading ? 'Saving...' : (mode === 'create' ? 'Create Agent' : 'Update Agent')}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AgentManagement;
