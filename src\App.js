import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Activities from './pages/Activities';
import Personnel from './pages/Personnel';
import Bookings from './pages/Bookings';
import Wallets from './pages/Wallets';
import AgentWallet from './pages/AgentWallet';
import ElephantaStaffDashboard from './pages/ElephantaStaffDashboard';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Public Route */}
            <Route path="/login" element={<Login />} />

            {/* Protected Routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/activities" element={
              <ProtectedRoute roles={['admin', 'manager']}>
                <Layout>
                  <Activities />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/personnel" element={
              <ProtectedRoute roles={['admin', 'manager']}>
                <Layout>
                  <Personnel />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/bookings" element={
              <ProtectedRoute>
                <Layout>
                  <Bookings />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/elephanta-dashboard" element={
              <ProtectedRoute roles={['elephanta_staff']}>
                <Layout>
                  <ElephantaStaffDashboard />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/agent-wallet" element={
              <ProtectedRoute roles={['agent']}>
                <Layout>
                  <AgentWallet />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/wallets" element={
              <ProtectedRoute roles={['admin', 'manager']}>
                <Layout>
                  <Wallets />
                </Layout>
              </ProtectedRoute>
            } />

            {/* Default redirect */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
          </Routes>

          {/* Toast notifications */}
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;