import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import activityService from '../../services/activityService';
import slotService from '../../services/slotService';
import toast from 'react-hot-toast';

const SlotModal = ({ isOpen, onClose, onSuccess }) => {
    const { user } = useAuth();
    const [formData, setFormData] = useState({
        activity_id: '',
        date: '',
        time: ''
    });
    const [activities, setActivities] = useState([]);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (isOpen) {
            fetchActivities();
            // Set default date to today
            setFormData(prev => ({
                ...prev,
                date: new Date().toISOString().split('T')[0]
            }));
        }
    }, [isOpen]);

    const fetchActivities = async () => {
        try {
            const activitiesData = await activityService.getActiveActivities();
            setActivities(activitiesData);
        } catch (error) {
            toast.error('Failed to load activities');
        }
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.activity_id) {
            newErrors.activity_id = 'Activity selection is required';
        }

        if (!formData.date) {
            newErrors.date = 'Date is required';
        } else {
            const selectedDate = new Date(formData.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            selectedDate.setHours(0, 0, 0, 0);

            if (selectedDate.getTime() !== today.getTime()) {
                newErrors.date = 'Only today\'s date is allowed for slot creation';
            }
        }

        if (!formData.time) {
            newErrors.time = 'Time is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            const selectedActivity = activities.find(a => a.id === parseInt(formData.activity_id));
            
            const slotData = {
                ...formData,
                activity_name: selectedActivity.name
            };

            await slotService.createSlot(slotData, user);
            toast.success('Slot created successfully!');
            
            if (onSuccess) {
                onSuccess();
            }
            
            resetForm();
        } catch (error) {
            toast.error(error.message || 'Failed to create slot');
        } finally {
            setLoading(false);
        }
    };

    const resetForm = () => {
        setFormData({
            activity_id: '',
            date: new Date().toISOString().split('T')[0],
            time: ''
        });
        setErrors({});
    };

    if (!isOpen) return null;

    return createPortal(
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        Create New Slot
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Activity *
                        </label>
                        <select
                            value={formData.activity_id}
                            onChange={(e) => handleInputChange('activity_id', e.target.value)}
                            className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                errors.activity_id ? 'border-red-300' : 'border-gray-300'
                            } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                        >
                            <option value="">Select an activity</option>
                            {activities.map((activity) => (
                                <option key={activity.id} value={activity.id}>
                                    {activity.name}
                                </option>
                            ))}
                        </select>
                        {errors.activity_id && <p className="mt-1 text-sm text-red-600">{errors.activity_id}</p>}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Date *
                        </label>
                        <input
                            type="date"
                            value={formData.date}
                            onChange={(e) => handleInputChange('date', e.target.value)}
                            min={new Date().toISOString().split('T')[0]}
                            max={new Date().toISOString().split('T')[0]}
                            className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                errors.date ? 'border-red-300' : 'border-gray-300'
                            } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                        />
                        {errors.date && <p className="mt-1 text-sm text-red-600">{errors.date}</p>}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Time *
                        </label>
                        <input
                            type="time"
                            value={formData.time}
                            onChange={(e) => handleInputChange('time', e.target.value)}
                            className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                errors.time ? 'border-red-300' : 'border-gray-300'
                            } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                        />
                        {errors.time && <p className="mt-1 text-sm text-red-600">{errors.time}</p>}
                    </div>

                    <div className="bg-blue-50 p-3 rounded-md">
                        <h4 className="text-sm font-medium text-blue-900 mb-2">Slot Information</h4>
                        <ul className="text-sm text-blue-800 space-y-1">
                            <li>• Capacity: 12 persons (Semi-submarine limit)</li>
                            <li>• Rate: ₹3,500 per person</li>
                            <li>• Commission: ₹{user?.commission_amount || 350} per booking</li>
                        </ul>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 bg-teal-600 text-white rounded-md text-sm font-medium hover:bg-teal-700 disabled:opacity-50"
                        >
                            {loading ? 'Creating...' : 'Create Slot'}
                        </button>
                    </div>
                </form>
            </div>
        </div>,
        document.body
    );
};

export default SlotModal;
