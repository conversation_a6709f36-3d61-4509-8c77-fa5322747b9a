{"name": "activity-crm-frontend", "version": "1.0.0", "description": "Activity CRM Dashboard with Wallet Management", "private": true, "dependencies": {"@heroicons/react": "^2.0.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-hot-toast": "^2.4.1", "axios": "^1.3.4", "@headlessui/react": "^1.7.13"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "devDependencies": {"react-scripts": "5.0.1", "tailwindcss": "^3.2.7", "@tailwindcss/forms": "^0.5.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.21"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}