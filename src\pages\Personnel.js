import React, { useState, useEffect } from 'react';
import { UsersIcon, UserIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import personnelService from '../services/personnelService';
import StaffManagement from '../components/personnel/StaffManagement';
import BoatBoyManagement from '../components/personnel/BoatBoyManagement';
import AgentManagement from '../components/personnel/AgentManagement';
import ElephantaStaffSettings from '../components/admin/ElephantaStaffSettings';
import toast from 'react-hot-toast';

const Personnel = () => {
    const [activeTab, setActiveTab] = useState('staff');
    const [stats, setStats] = useState({
        staff: { total: 0, active: 0, inactive: 0, total_wallet: 0 },
        boat_boys: { total: 0, active: 0, inactive: 0, total_wallet: 0 },
        agents: { total: 0, active: 0, inactive: 0, total_wallet: 0 }
    });
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchStats();
    }, []);

    const fetchStats = async () => {
        try {
            setLoading(true);
            const [staffResponse, boatBoyResponse, agentResponse] = await Promise.all([
                personnelService.getStaff(),
                personnelService.getBoatBoys(),
                personnelService.getAgents()
            ]);

            setStats({
                staff: {
                    total: staffResponse.total,
                    active: staffResponse.active_count,
                    inactive: staffResponse.inactive_count,
                    total_wallet: staffResponse.total_wallet_balance
                },
                boat_boys: {
                    total: boatBoyResponse.total,
                    active: boatBoyResponse.active_count,
                    inactive: boatBoyResponse.inactive_count,
                    total_wallet: boatBoyResponse.total_wallet_balance
                },
                agents: {
                    total: agentResponse.total,
                    active: agentResponse.active_count,
                    inactive: agentResponse.inactive_count,
                    total_wallet: agentResponse.total_wallet_balance
                }
            });
        } catch (error) {
            toast.error('Failed to fetch personnel statistics');
        } finally {
            setLoading(false);
        }
    };

    const tabs = [
        { id: 'staff', name: 'Staff Management', icon: UsersIcon },
        { id: 'boat_boys', name: 'Boat Boy Management', icon: UserIcon },
        { id: 'agents', name: 'Agent Management', icon: UserGroupIcon },
        { id: 'elephanta_settings', name: 'Elephanta Staff Settings', icon: UsersIcon }
    ];

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div>
                <h1 className="text-2xl font-bold text-gray-900">Personnel Management</h1>
                <p className="mt-2 text-sm text-gray-700">
                    Manage staff members and boat boys, track their status and wallet summaries
                </p>
            </div>

            {/* Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
                {/* Staff Stats */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <UsersIcon className="h-8 w-8 text-blue-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Total Staff</p>
                            <p className="text-2xl font-semibold text-gray-900">{stats.staff.total}</p>
                            <p className="text-xs text-gray-500">
                                {stats.staff.active} active, {stats.staff.inactive} inactive
                            </p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span className="text-green-600 font-semibold text-xs">₹</span>
                            </div>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Staff Wallet Balance</p>
                            <p className="text-2xl font-semibold text-gray-900">
                                ₹{stats.staff.total_wallet.toLocaleString()}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Boat Boy Stats */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <UserIcon className="h-8 w-8 text-teal-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Total Boat Boys</p>
                            <p className="text-2xl font-semibold text-gray-900">{stats.boat_boys.total}</p>
                            <p className="text-xs text-gray-500">
                                {stats.boat_boys.active} active, {stats.boat_boys.inactive} inactive
                            </p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                <span className="text-orange-600 font-semibold text-xs">₹</span>
                            </div>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Boat Boy Wallet Balance</p>
                            <p className="text-2xl font-semibold text-gray-900">
                                ₹{stats.boat_boys.total_wallet.toLocaleString()}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Agent Stats */}
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <UserGroupIcon className="h-8 w-8 text-purple-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Total Agents</p>
                            <p className="text-2xl font-semibold text-gray-900">{stats.agents.total}</p>
                            <p className="text-xs text-gray-500">
                                {stats.agents.active} active, {stats.agents.inactive} inactive
                            </p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span className="text-purple-600 font-semibold text-xs">₹</span>
                            </div>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500">Agent Wallet Balance</p>
                            <p className="text-2xl font-semibold text-gray-900">
                                ₹{stats.agents.total_wallet.toLocaleString()}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Tab Navigation */}
            <div className="bg-white shadow rounded-lg">
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        {tabs.map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`${
                                        activeTab === tab.id
                                            ? 'border-teal-500 text-teal-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                                >
                                    <Icon className="h-5 w-5 mr-2" />
                                    {tab.name}
                                </button>
                            );
                        })}
                    </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                    {activeTab === 'staff' && (
                        <StaffManagement onStatsUpdate={fetchStats} />
                    )}
                    {activeTab === 'boat_boys' && (
                        <BoatBoyManagement onStatsUpdate={fetchStats} />
                    )}
                    {activeTab === 'agents' && (
                        <AgentManagement onStatsUpdate={fetchStats} />
                    )}
                    {activeTab === 'elephanta_settings' && (
                        <ElephantaStaffSettings />
                    )}
                </div>
            </div>
        </div>
    );
};

export default Personnel;
