import React, { useState, useEffect } from 'react';
import {
    PlusIcon,
    UsersIcon,
    CurrencyRupeeIcon,
    CalendarIcon,
    UserGroupIcon,
    ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import slotService from '../services/slotService';
import SlotModal from '../components/slots/SlotModal';
import ParticipantModal from '../components/slots/ParticipantModal';
import TransferModal from '../components/slots/TransferModal';
import SlotsListModal from '../components/slots/SlotsListModal';
import ParticipantsListModal from '../components/slots/ParticipantsListModal';
import PassengerManagement from '../components/passengers/PassengerManagement';
import toast from 'react-hot-toast';

const ElephantaStaffDashboard = () => {
    const { user } = useAuth();
    const [activeTab, setActiveTab] = useState('slots');
    const [slots, setSlots] = useState([]);
    const [stats, setStats] = useState({});
    const [paymentSummary, setPaymentSummary] = useState({});
    const [loading, setLoading] = useState(true);
    const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
    const [showSlotModal, setShowSlotModal] = useState(false);
    const [showParticipantModal, setShowParticipantModal] = useState(false);
    const [showTransferModal, setShowTransferModal] = useState(false);
    const [showSlotsListModal, setShowSlotsListModal] = useState(false);
    const [showParticipantsListModal, setShowParticipantsListModal] = useState(false);
    const [selectedSlot, setSelectedSlot] = useState(null);
    const [transferring, setTransferring] = useState(false);

    const tabs = [
        {
            id: 'slots',
            name: 'Slot Management',
            icon: ClipboardDocumentListIcon,
            description: 'Manage slots, collect payments, and handle cancellations'
        },
        {
            id: 'passengers',
            name: 'Passenger Management',
            icon: UserGroupIcon,
            description: 'Track passenger arrivals, departures, and completed trips'
        }
    ];

    useEffect(() => {
        fetchData();
    }, [selectedDate]); // eslint-disable-line react-hooks/exhaustive-deps

    const fetchData = async () => {
        try {
            setLoading(true);
            const [slotsData, paymentData] = await Promise.all([
                slotService.getSlots({ date: selectedDate }),
                slotService.getPaymentSummary(user)
            ]);

            // Calculate stats from the filtered slots data
            const statsData = {
                total_slots: slotsData.length,
                active_slots: slotsData.filter(s => s.status === 'active').length,
                cancelled_slots: slotsData.filter(s => s.status === 'cancelled').length,
                completed_slots: slotsData.filter(s => s.status === 'completed').length,
                total_participants: slotsData.reduce((sum, slot) => sum + slot.booked_count, 0),
                total_revenue: slotsData.reduce((sum, slot) => sum + slot.collected_amount, 0),
                total_commission: slotsData.reduce((sum, slot) => sum + slot.commission_amount, 0),
                pending_amount: slotsData.reduce((sum, slot) => sum + (slot.total_amount - slot.collected_amount), 0)
            };
            setSlots(slotsData);
            setStats(statsData);
            setPaymentSummary(paymentData);
        } catch (error) {
            toast.error('Failed to fetch data');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateSlot = () => {
        setSelectedSlot(null);
        setShowSlotModal(true);
    };

    const handleAddParticipant = (slot) => {
        setSelectedSlot(slot);
        setShowParticipantModal(true);
    };

    const handleCollectPayment = async (slotId, participantId) => {
        try {
            await slotService.collectPayment(slotId, participantId, user);
            toast.success('Payment collected successfully!');
            fetchData();
        } catch (error) {
            toast.error(error.message || 'Failed to collect payment');
        }
    };

    const handleRefundParticipant = async (slotId, participantId) => {
        const reason = prompt('Please enter refund reason:');
        if (!reason) return;

        try {
            await slotService.refundParticipant(slotId, participantId, reason, user);
            toast.success('Participant refunded successfully!');
            fetchData();
        } catch (error) {
            toast.error(error.message || 'Failed to process refund');
        }
    };

    const handleCancelSlot = async (slotId) => {
        const reason = prompt('Please enter cancellation reason (e.g., Weather conditions):');
        if (!reason) return;

        try {
            await slotService.cancelSlot(slotId, reason, user);
            toast.success('Slot cancelled and refunds processed!');
            fetchData();
        } catch (error) {
            toast.error(error.message || 'Failed to cancel slot');
        }
    };

    const handleDeleteSlot = async (slotId) => {
        const confirmed = window.confirm(
            'Are you sure you want to permanently delete this cancelled slot?\n\nThis action cannot be undone.'
        );

        if (!confirmed) return;

        try {
            await slotService.deleteSlot(slotId, user);
            toast.success('Slot deleted permanently!');
            fetchData();
        } catch (error) {
            toast.error(error.message || 'Failed to delete slot');
        }
    };

    const handleTransferToAdmin = () => {
        if (paymentSummary.pending_transfer <= 0) {
            toast.error('No pending amount to transfer');
            return;
        }
        setShowTransferModal(true);
    };

    const handleConfirmTransfer = async (transferMode) => {
        try {
            setTransferring(true);
            await slotService.transferToAdmin(user, transferMode);
            toast.success(`Money transferred to Admin via ${transferMode.replace('_', ' ')} successfully!`);
            fetchData();
            setShowTransferModal(false);
        } catch (error) {
            toast.error(error.message || 'Failed to transfer money');
        } finally {
            setTransferring(false);
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            case 'completed': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getPaymentStatusColor = (status) => {
        switch (status) {
            case 'collected': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'refunded': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Elephanta Staff Dashboard</h1>
                    <p className="text-gray-600">{tabs.find(t => t.id === activeTab)?.description}</p>
                </div>
                {activeTab === 'slots' && (
                    <button
                        onClick={handleCreateSlot}
                        className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 flex items-center gap-2"
                    >
                        <PlusIcon className="h-5 w-5" />
                        Create New Slot
                    </button>
                )}
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                    {tabs.map((tab) => {
                        const Icon = tab.icon;
                        const isActive = activeTab === tab.id;

                        return (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                                    isActive
                                        ? 'border-teal-500 text-teal-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <Icon className={`-ml-0.5 mr-2 h-5 w-5 ${isActive ? 'text-teal-500' : 'text-gray-400 group-hover:text-gray-500'}`} />
                                {tab.name}
                            </button>
                        );
                    })}
                </nav>
            </div>

            {/* Tab Content */}
            {activeTab === 'slots' && (
                <>
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div
                    className="bg-white p-6 rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => setShowSlotsListModal(true)}
                >
                    <div className="flex items-center">
                        <div className="p-2 bg-blue-100 rounded-lg">
                            <CalendarIcon className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Total Slots</p>
                            <p className="text-2xl font-bold text-gray-900">{stats.total_slots || 0}</p>
                            <p className="text-xs text-blue-600 mt-1">Click to view all slots</p>
                        </div>
                    </div>
                </div>

                <div
                    className="bg-white p-6 rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => setShowParticipantsListModal(true)}
                >
                    <div className="flex items-center">
                        <div className="p-2 bg-green-100 rounded-lg">
                            <UsersIcon className="h-6 w-6 text-green-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Total Participants</p>
                            <p className="text-2xl font-bold text-gray-900">{stats.total_participants || 0}</p>
                            <p className="text-xs text-green-600 mt-1">Click to view all participants</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center">
                        <div className="p-2 bg-yellow-100 rounded-lg">
                            <CurrencyRupeeIcon className="h-6 w-6 text-yellow-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                            <p className="text-2xl font-bold text-gray-900">₹{(stats.total_revenue || 0).toLocaleString()}</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center">
                        <div className="p-2 bg-purple-100 rounded-lg">
                            <CurrencyRupeeIcon className="h-6 w-6 text-purple-600" />
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">My Commission</p>
                            <p className="text-2xl font-bold text-gray-900">₹{(paymentSummary.total_commission_earned || 0).toLocaleString()}</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Payment Summary */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-semibold text-gray-900">Payment Summary</h2>
                    {paymentSummary.pending_transfer > 0 && (
                        <button
                            onClick={handleTransferToAdmin}
                            disabled={transferring}
                            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
                        >
                            {transferring ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    Transferring...
                                </>
                            ) : (
                                <>
                                    <CurrencyRupeeIcon className="h-4 w-4" />
                                    Transfer to Admin
                                </>
                            )}
                        </button>
                    )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm text-gray-600">Total Collected</p>
                        <p className="text-lg font-semibold text-blue-600">₹{(paymentSummary.total_collected || 0).toLocaleString()}</p>
                    </div>
                    <div className="text-center p-3 bg-yellow-50 rounded-lg">
                        <p className="text-sm text-gray-600">Pending Transfer</p>
                        <p className="text-lg font-semibold text-yellow-600">₹{(paymentSummary.pending_transfer || 0).toLocaleString()}</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                        <p className="text-sm text-gray-600">Commission Earned</p>
                        <p className="text-lg font-semibold text-green-600">₹{(paymentSummary.total_commission_earned || 0).toLocaleString()}</p>
                    </div>
                    <div className="text-center p-3 bg-red-50 rounded-lg">
                        <p className="text-sm text-gray-600">Total Refunded</p>
                        <p className="text-lg font-semibold text-red-600">₹{(paymentSummary.total_refunded || 0).toLocaleString()}</p>
                    </div>
                </div>
            </div>

            {/* Date Filter */}
            <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center gap-4">
                    <label className="text-sm font-medium text-gray-700">Filter by Date:</label>
                    <input
                        type="date"
                        value={selectedDate}
                        onChange={(e) => setSelectedDate(e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                    />
                    <span className="text-sm text-gray-600">
                        Showing {slots.length} slot(s) for {new Date(selectedDate).toLocaleDateString()}
                    </span>
                </div>
            </div>

            {/* Slots List */}
            <div className="space-y-4">
                {slots.length === 0 ? (
                    <div className="bg-white p-8 rounded-lg shadow-sm border text-center">
                        <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No slots found</h3>
                        <p className="text-gray-600 mb-4">Create a new slot to start managing participants</p>
                        <button
                            onClick={handleCreateSlot}
                            className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700"
                        >
                            Create First Slot
                        </button>
                    </div>
                ) : (
                    slots.map((slot) => (
                        <div key={slot.id} className="bg-white rounded-lg shadow-sm border">
                            <div className="p-6">
                                <div className="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-900">{slot.activity_name}</h3>
                                        <p className="text-gray-600">{slot.date} at {slot.time}</p>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(slot.status)}`}>
                                            {slot.status.charAt(0).toUpperCase() + slot.status.slice(1)}
                                        </span>
                                        {slot.status === 'active' && (
                                            <button
                                                onClick={() => handleCancelSlot(slot.id)}
                                                className="text-orange-600 hover:text-orange-800 text-sm"
                                            >
                                                Cancel Slot
                                            </button>
                                        )}
                                        {slot.status === 'cancelled' && (
                                            <button
                                                onClick={() => handleDeleteSlot(slot.id)}
                                                className="text-red-600 hover:text-red-800 text-sm"
                                            >
                                                Delete
                                            </button>
                                        )}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                        <p className="text-sm text-gray-600">Capacity</p>
                                        <p className="text-lg font-semibold">{slot.capacity}</p>
                                    </div>
                                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                                        <p className="text-sm text-gray-600">Booked</p>
                                        <p className="text-lg font-semibold text-blue-600">{slot.booked_count}</p>
                                    </div>
                                    <div className="text-center p-3 bg-green-50 rounded-lg">
                                        <p className="text-sm text-gray-600">Available</p>
                                        <p className="text-lg font-semibold text-green-600">{slot.available_count}</p>
                                    </div>
                                    <div className="text-center p-3 bg-yellow-50 rounded-lg">
                                        <p className="text-sm text-gray-600">Collected</p>
                                        <p className="text-lg font-semibold text-yellow-600">₹{slot.collected_amount.toLocaleString()}</p>
                                    </div>
                                </div>

                                {slot.status === 'active' && slot.available_count > 0 && (
                                    <div className="mb-4">
                                        <button
                                            onClick={() => handleAddParticipant(slot)}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                                        >
                                            <PlusIcon className="h-4 w-4" />
                                            Add Participant
                                        </button>
                                    </div>
                                )}

                                {/* Participants List */}
                                {slot.participants.length > 0 && (
                                    <div>
                                        <h4 className="text-md font-medium text-gray-900 mb-3">Participants</h4>
                                        <div className="space-y-2">
                                            {slot.participants.map((participant) => (
                                                <div key={participant.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                                    <div>
                                                        <p className="font-medium">{participant.name}</p>
                                                        <p className="text-sm text-gray-600">
                                                            {participant.contact} • {participant.total_persons} person(s) • ₹{participant.total_amount.toLocaleString()}
                                                        </p>
                                                        <div className="flex gap-3 text-xs">
                                                            {participant.ticket_id && (
                                                                <span className="text-purple-600">🎫 {participant.ticket_id}</span>
                                                            )}
                                                            {participant.boat_name && (
                                                                <span className="text-blue-600">🚢 {participant.boat_name}</span>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(participant.payment_status)}`}>
                                                            {participant.payment_status.charAt(0).toUpperCase() + participant.payment_status.slice(1)}
                                                        </span>
                                                        {participant.payment_status === 'pending' && slot.status === 'active' && (
                                                            <button
                                                                onClick={() => handleCollectPayment(slot.id, participant.id)}
                                                                className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                                                            >
                                                                Collect Payment
                                                            </button>
                                                        )}
                                                        {participant.payment_status === 'collected' && slot.status === 'active' && (
                                                            <button
                                                                onClick={() => handleRefundParticipant(slot.id, participant.id)}
                                                                className="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 ml-2"
                                                            >
                                                                Refund
                                                            </button>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))
                )}
            </div>

            {/* Modals */}
            <SlotModal
                isOpen={showSlotModal}
                onClose={() => setShowSlotModal(false)}
                onSuccess={() => {
                    setShowSlotModal(false);
                    fetchData();
                }}
            />

            <ParticipantModal
                isOpen={showParticipantModal}
                onClose={() => setShowParticipantModal(false)}
                slot={selectedSlot}
                onSuccess={() => {
                    setShowParticipantModal(false);
                    fetchData();
                }}
            />

            <TransferModal
                isOpen={showTransferModal}
                onClose={() => setShowTransferModal(false)}
                onConfirm={handleConfirmTransfer}
                transferAmount={paymentSummary.pending_transfer || 0}
                commissionAmount={Math.floor((paymentSummary.pending_transfer || 0) / 3500) * (user?.commission_amount || 350)}
                bookingCount={Math.floor((paymentSummary.pending_transfer || 0) / 3500)}
            />

            <SlotsListModal
                isOpen={showSlotsListModal}
                onClose={() => setShowSlotsListModal(false)}
                slots={slots}
            />

            <ParticipantsListModal
                isOpen={showParticipantsListModal}
                onClose={() => setShowParticipantsListModal(false)}
                slots={slots}
            />
                </>
            )}

            {/* Passenger Management Tab */}
            {activeTab === 'passengers' && (
                <PassengerManagement />
            )}

        </div>
    );
};

export default ElephantaStaffDashboard;
