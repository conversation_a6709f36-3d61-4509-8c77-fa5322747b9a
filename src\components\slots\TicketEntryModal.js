import React, { useState } from 'react';
import { XMarkIcon, TicketIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import slotService from '../../services/slotService';
import toast from 'react-hot-toast';

const TicketEntryModal = ({ isOpen, onClose, onSuccess }) => {
    const { user } = useAuth();
    const [formData, setFormData] = useState({
        ticket_id: '',
        boat_name: ''
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.ticket_id.trim()) {
            newErrors.ticket_id = 'Ticket ID is required';
        }

        if (!formData.boat_name.trim()) {
            newErrors.boat_name = 'Boat name is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            const result = await slotService.addParticipantByTicket(
                formData.ticket_id.toUpperCase(),
                formData.boat_name,
                user
            );

            toast.success(result.message);
            
            if (onSuccess) {
                onSuccess(result);
            }
            
            handleClose();
        } catch (error) {
            toast.error(error.message || 'Failed to add participant');
        } finally {
            setLoading(false);
        }
    };

    const resetForm = () => {
        setFormData({
            ticket_id: '',
            boat_name: ''
        });
        setErrors({});
        setLoading(false);
    };

    const handleClose = () => {
        resetForm();
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                        <TicketIcon className="h-5 w-5 text-teal-600" />
                        Add Participant by Ticket
                    </h3>
                    <button
                        onClick={handleClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <div className="bg-blue-50 p-3 rounded-md mb-4">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">How it works:</h4>
                    <ul className="text-xs text-blue-800 space-y-1">
                        <li>• Enter ticket ID to automatically add participant</li>
                        <li>• System finds available slot or creates new one</li>
                        <li>• Max 12 people per slot</li>
                        <li>• Overflow goes to next slot automatically</li>
                    </ul>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Ticket ID *
                        </label>
                        <input
                            type="text"
                            value={formData.ticket_id}
                            onChange={(e) => handleInputChange('ticket_id', e.target.value.toUpperCase())}
                            className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                errors.ticket_id ? 'border-red-300' : 'border-gray-300'
                            } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                            placeholder="e.g., TKT001"
                        />
                        {errors.ticket_id && <p className="mt-1 text-sm text-red-600">{errors.ticket_id}</p>}
                        <p className="mt-1 text-xs text-gray-500">
                            Enter the ticket number to add participant automatically
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Boat Name *
                        </label>
                        <input
                            type="text"
                            value={formData.boat_name}
                            onChange={(e) => handleInputChange('boat_name', e.target.value)}
                            className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                errors.boat_name ? 'border-red-300' : 'border-gray-300'
                            } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                            placeholder="e.g., Semi-Submarine A"
                        />
                        {errors.boat_name && <p className="mt-1 text-sm text-red-600">{errors.boat_name}</p>}
                        <p className="mt-1 text-xs text-gray-500">
                            Specify which boat this participant will use
                        </p>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="text-sm text-yellow-800">
                            <strong>Note:</strong> The system will automatically find an available slot 
                            for the ticket date or create a new slot if needed. If the current slot 
                            is full (12 people), the participant will be added to the next available slot.
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={handleClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 bg-teal-600 text-white rounded-md text-sm font-medium hover:bg-teal-700 disabled:opacity-50 flex items-center gap-2"
                        >
                            {loading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    Adding...
                                </>
                            ) : (
                                <>
                                    <TicketIcon className="h-4 w-4" />
                                    Add Participant
                                </>
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default TicketEntryModal;
