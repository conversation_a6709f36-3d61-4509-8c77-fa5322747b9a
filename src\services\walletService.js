// Mock data service for wallet management
// This simulates backend API calls with local data

class WalletService {
    constructor() {
        // Initialize with sample data in localStorage
        this.initializeSampleData();
    }

    // Sample data initialization
    initializeSampleData() {
        if (!localStorage.getItem('activity_crm_wallets')) {
            const sampleData = {
                staff_wallets: [
                    {
                        id: 1,
                        personnel_id: 1,
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        employee_id: 'EMP001',
                        balance: 2500.00,
                        total_earned: 12000.00,
                        total_paid: 9500.00,
                        last_transaction_at: '2025-01-05',
                        pending_amount: 0
                    },
                    {
                        id: 2,
                        personnel_id: 2,
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        employee_id: 'EMP002',
                        balance: 1800.00,
                        total_earned: 8500.00,
                        total_paid: 6700.00,
                        last_transaction_at: '2025-01-03',
                        pending_amount: 0
                    },
                    {
                        id: 3,
                        personnel_id: 3,
                        name: '<PERSON>',
                        email: '<EMAIL>',
                        employee_id: 'EMP003',
                        balance: 3200.00,
                        total_earned: 15000.00,
                        total_paid: 11800.00,
                        last_transaction_at: '2025-01-04',
                        pending_amount: 0
                    }
                ],
                boat_boy_wallets: [
                    {
                        id: 4,
                        personnel_id: 1,
                        name: 'Ram Kumar',
                        email: '<EMAIL>',
                        license_number: 'BL001-2023',
                        balance: 1500.00,
                        total_earned: 6000.00,
                        total_paid: 4500.00,
                        last_transaction_at: '2025-01-02',
                        pending_amount: 0
                    },
                    {
                        id: 5,
                        personnel_id: 2,
                        name: 'Shyam Prasad',
                        email: '<EMAIL>',
                        license_number: 'BL002-2023',
                        balance: 2100.00,
                        total_earned: 7500.00,
                        total_paid: 5400.00,
                        last_transaction_at: '2025-01-01',
                        pending_amount: 0
                    },
                    {
                        id: 6,
                        personnel_id: 3,
                        name: 'Vijay Singh',
                        email: '<EMAIL>',
                        license_number: 'BL003-2023',
                        balance: 900.00,
                        total_earned: 4200.00,
                        total_paid: 3300.00,
                        last_transaction_at: '2024-12-30',
                        pending_amount: 0
                    }
                ],
                agent_wallets: [
                    {
                        id: 7,
                        personnel_id: 1,
                        name: 'Rajesh Kumar',
                        company_name: 'ABC Tours & Travels',
                        email: '<EMAIL>',
                        balance: 5600.00,
                        total_earned: 25000.00,
                        total_paid: 19400.00,
                        last_transaction_at: '2025-01-04',
                        pending_amount: 0
                    },
                    {
                        id: 8,
                        personnel_id: 2,
                        name: 'Priya Sharma',
                        company_name: 'XYZ Travel Agency',
                        email: '<EMAIL>',
                        balance: 3400.00,
                        total_earned: 18000.00,
                        total_paid: 14600.00,
                        last_transaction_at: '2025-01-03',
                        pending_amount: 0
                    },
                    {
                        id: 9,
                        personnel_id: 3,
                        name: 'Suresh Gupta',
                        company_name: 'Island Explorer Tours',
                        email: '<EMAIL>',
                        balance: 4200.00,
                        total_earned: 21000.00,
                        total_paid: 16800.00,
                        last_transaction_at: '2025-01-02',
                        pending_amount: 0
                    }
                ],
                transactions: [
                    {
                        id: 1,
                        wallet_id: 1,
                        amount: 500.00,
                        type: 'debit',
                        status: 'paid',
                        payment_method: 'bank_transfer',
                        reference_number: 'PAY-**********',
                        description: 'Salary advance payment',
                        processed_by: 'Admin User',
                        processed_at: '2025-01-05 14:30:00',
                        personnel_type: 'staff',
                        personnel_name: 'John Doe'
                    },
                    {
                        id: 2,
                        wallet_id: 7,
                        amount: 1200.00,
                        type: 'debit',
                        status: 'paid',
                        payment_method: 'upi',
                        reference_number: 'PAY-**********',
                        description: 'Commission payment',
                        processed_by: 'Manager User',
                        processed_at: '2025-01-04 16:45:00',
                        personnel_type: 'agent',
                        personnel_name: 'Rajesh Kumar'
                    }
                ]
            };

            localStorage.setItem('activity_crm_wallets', JSON.stringify(sampleData));
        }
    }

    // Get all wallet data from localStorage
    getWalletData() {
        const data = localStorage.getItem('activity_crm_wallets');
        return data ? JSON.parse(data) : null;
    }

    // Update wallet data in localStorage
    updateWalletData(data) {
        localStorage.setItem('activity_crm_wallets', JSON.stringify(data));
    }

    // Simulate API delay
    delay(ms = 500) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get staff wallets
    async getStaffWallets() {
        await this.delay();
        const data = this.getWalletData();
        return {
            staff_wallets: data.staff_wallets,
            total_staff: data.staff_wallets.length,
            total_balance: data.staff_wallets.reduce((sum, wallet) => sum + wallet.balance, 0),
            total_pending: data.staff_wallets.reduce((sum, wallet) => sum + wallet.pending_amount, 0)
        };
    }

    // Get boat boy wallets
    async getBoatBoyWallets() {
        await this.delay();
        const data = this.getWalletData();
        return {
            boat_boy_wallets: data.boat_boy_wallets,
            total_boat_boys: data.boat_boy_wallets.length,
            total_balance: data.boat_boy_wallets.reduce((sum, wallet) => sum + wallet.balance, 0),
            total_pending: data.boat_boy_wallets.reduce((sum, wallet) => sum + wallet.pending_amount, 0)
        };
    }

    // Get agent wallets
    async getAgentWallets() {
        await this.delay();
        const data = this.getWalletData();
        return {
            agent_wallets: data.agent_wallets,
            total_agents: data.agent_wallets.length,
            total_balance: data.agent_wallets.reduce((sum, wallet) => sum + wallet.balance, 0),
            total_pending: data.agent_wallets.reduce((sum, wallet) => sum + wallet.pending_amount, 0)
        };
    }

    // Release payment
    async releasePayment(paymentData) {
        await this.delay();
        const data = this.getWalletData();

        // Find the wallet
        let wallet = null;
        let walletArray = null;

        // Search in all wallet types
        if (data.staff_wallets.find(w => w.id === paymentData.wallet_id)) {
            walletArray = data.staff_wallets;
            wallet = walletArray.find(w => w.id === paymentData.wallet_id);
        } else if (data.boat_boy_wallets.find(w => w.id === paymentData.wallet_id)) {
            walletArray = data.boat_boy_wallets;
            wallet = walletArray.find(w => w.id === paymentData.wallet_id);
        } else if (data.agent_wallets.find(w => w.id === paymentData.wallet_id)) {
            walletArray = data.agent_wallets;
            wallet = walletArray.find(w => w.id === paymentData.wallet_id);
        }

        if (!wallet) {
            throw new Error('Wallet not found');
        }

        if (paymentData.amount > wallet.balance) {
            throw new Error('Insufficient balance');
        }

        // Update wallet balance
        wallet.balance -= paymentData.amount;
        wallet.total_paid += paymentData.amount;
        wallet.last_transaction_at = new Date().toISOString().split('T')[0];

        // Create transaction record
        const transaction = {
            id: data.transactions.length + 1,
            wallet_id: wallet.id,
            amount: paymentData.amount,
            type: 'debit',
            status: 'paid',
            payment_method: paymentData.payment_method,
            reference_number: paymentData.reference_number || `PAY-${Date.now()}`,
            description: `Payment released by Admin`,
            notes: paymentData.notes || '',
            processed_by: 'Current User',
            processed_at: new Date().toISOString(),
            personnel_type: paymentData.personnel_type || 'staff',
            personnel_name: wallet.name
        };

        data.transactions.unshift(transaction);

        // Save updated data
        this.updateWalletData(data);

        return {
            success: true,
            transaction,
            updated_balance: wallet.balance,
            message: 'Payment released successfully'
        };
    }

    // Get transactions
    async getTransactions(filters = {}) {
        await this.delay();
        const data = this.getWalletData();
        let transactions = [...data.transactions];

        // Apply filters
        if (filters.personnel_type) {
            transactions = transactions.filter(t => t.personnel_type === filters.personnel_type);
        }

        if (filters.date_from) {
            transactions = transactions.filter(t => t.processed_at >= filters.date_from);
        }

        if (filters.date_to) {
            const dateTo = new Date(filters.date_to);
            dateTo.setHours(23, 59, 59, 999);
            transactions = transactions.filter(t => new Date(t.processed_at) <= dateTo);
        }

        if (filters.payment_method) {
            transactions = transactions.filter(t => t.payment_method === filters.payment_method);
        }

        // Sort by date (newest first)
        transactions.sort((a, b) => new Date(b.processed_at) - new Date(a.processed_at));

        return {
            data: transactions,
            total: transactions.length
        };
    }

    // Get payment report
    async getPaymentReport(filters = {}) {
        await this.delay();
        const transactions = await this.getTransactions(filters);

        // Group transactions by personnel type
        const reportData = {};

        transactions.data.forEach(transaction => {
            const type = transaction.personnel_type;
            if (!reportData[type]) {
                reportData[type] = {
                    personnel_type: type,
                    total_paid: 0,
                    transaction_count: 0,
                    personnel: {}
                };
            }

            reportData[type].total_paid += transaction.amount;
            reportData[type].transaction_count++;

            if (!reportData[type].personnel[transaction.wallet_id]) {
                reportData[type].personnel[transaction.wallet_id] = {
                    details: { name: transaction.personnel_name },
                    total_paid: 0,
                    transactions: []
                };
            }

            reportData[type].personnel[transaction.wallet_id].total_paid += transaction.amount;
            reportData[type].personnel[transaction.wallet_id].transactions.push(transaction);
        });

        // Convert to array format
        const reportArray = Object.values(reportData).map(typeData => ({
            ...typeData,
            personnel: Object.values(typeData.personnel)
        }));

        return {
            report_data: reportArray,
            summary: {
                date_from: filters.date_from || '',
                date_to: filters.date_to || '',
                total_paid: transactions.data.reduce((sum, t) => sum + t.amount, 0),
                total_transactions: transactions.data.length,
                personnel_types: Object.keys(reportData)
            }
        };
    }

    // Get wallet summary
    async getWalletSummary() {
        await this.delay();
        const data = this.getWalletData();

        const allWallets = [
            ...data.staff_wallets,
            ...data.boat_boy_wallets,
            ...data.agent_wallets
        ];

        return {
            total_wallets: allWallets.length,
            total_balance: allWallets.reduce((sum, w) => sum + w.balance, 0),
            total_earned: allWallets.reduce((sum, w) => sum + w.total_earned, 0),
            total_paid: allWallets.reduce((sum, w) => sum + w.total_paid, 0),
            pending_transactions: 0,
            pending_amount: 0,
            by_personnel_type: {
                staff: {
                    count: data.staff_wallets.length,
                    balance: data.staff_wallets.reduce((sum, w) => sum + w.balance, 0),
                    earned: data.staff_wallets.reduce((sum, w) => sum + w.total_earned, 0)
                },
                boat_boy: {
                    count: data.boat_boy_wallets.length,
                    balance: data.boat_boy_wallets.reduce((sum, w) => sum + w.balance, 0),
                    earned: data.boat_boy_wallets.reduce((sum, w) => sum + w.total_earned, 0)
                },
                agent: {
                    count: data.agent_wallets.length,
                    balance: data.agent_wallets.reduce((sum, w) => sum + w.balance, 0),
                    earned: data.agent_wallets.reduce((sum, w) => sum + w.total_earned, 0)
                }
            }
        };
    }
}

// Export singleton instance
const walletService = new WalletService();
export default walletService;