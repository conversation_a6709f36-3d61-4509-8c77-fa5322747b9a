import React, { useState, useEffect, useCallback } from 'react';
import walletService from '../../services/walletService';
import {
    ChartBarIcon,
    DocumentArrowDownIcon,
    CalendarIcon,
    FunnelIcon,
    CurrencyDollarIcon
} from '@heroicons/react/24/outline';

const PaymentReports = () => {
    const [reportData, setReportData] = useState(null);
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [filters, setFilters] = useState({
        date_from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
        date_to: new Date().toISOString().split('T')[0],
        personnel_type: '',
        payment_method: ''
    });
    const [activeView, setActiveView] = useState('summary');

    const fetchPaymentReport = useCallback(async () => {
        try {
            setLoading(true);
            const response = await walletService.getPaymentReport(filters);
            setReportData(response);
        } catch (error) {
            console.error('Failed to fetch payment report:', error);
        } finally {
            setLoading(false);
        }
    }, [filters]);

    const fetchTransactions = useCallback(async () => {
        try {
            const response = await walletService.getTransactions({
                ...filters,
                status: 'paid',
                type: 'debit'
            });
            setTransactions(response.data || []);
        } catch (error) {
            console.error('Failed to fetch transactions:', error);
        }
    }, [filters]);

    useEffect(() => {
        fetchPaymentReport();
        fetchTransactions();
    }, [fetchPaymentReport, fetchTransactions]);

    const handleFilterChange = (name, value) => {
        setFilters(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleGenerateReport = () => {
        fetchPaymentReport();
        fetchTransactions();
        alert('Report generated successfully!');
    };

    const exportToCSV = () => {
        if (!transactions.length) {
            alert('No data to export');
            return;
        }

        const csvData = transactions.map(transaction => ({
            Date: new Date(transaction.processed_at).toLocaleDateString(),
            'Personnel Name': transaction.personnel_name || 'N/A',
            'Personnel Type': transaction.personnel_type || 'N/A',
            Amount: transaction.amount,
            'Payment Method': transaction.payment_method || 'N/A',
            'Reference Number': transaction.reference_number || 'N/A',
            'Processed By': transaction.processed_by || 'N/A'
        }));

        const csv = [
            Object.keys(csvData[0]).join(','),
            ...csvData.map(row => Object.values(row).join(','))
        ].join('\n');

        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `payment-report-${filters.date_from}-to-${filters.date_to}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    };

    const personnelTypes = [
        { value: '', label: 'All Personnel Types' },
        { value: 'staff', label: 'Staff' },
        { value: 'boat_boy', label: 'Boat Boys' },
        { value: 'agent', label: 'Agents' }
    ];

    const paymentMethods = [
        { value: '', label: 'All Payment Methods' },
        { value: 'bank_transfer', label: 'Bank Transfer' },
        { value: 'cash', label: 'Cash' },
        { value: 'cheque', label: 'Cheque' },
        { value: 'upi', label: 'UPI' }
    ];

    return (
        <div className="space-y-6">
            {/* Header with Demo Notice */}
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                <div className="flex">
                    <div className="ml-3">
                        <p className="text-sm text-blue-700">
                            <strong>Demo Mode:</strong> This report uses sample data stored locally. 
                            Payment release actions will be reflected here.
                        </p>
                    </div>
                </div>
            </div>

            {/* Header with Filters */}
            <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Payment Reports</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        Generate and view payment reports with filters
                    </p>
                </div>

                {/* Filters */}
                <div className="p-6">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">From Date</label>
                            <input
                                type="date"
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                                value={filters.date_from}
                                onChange={(e) => handleFilterChange('date_from', e.target.value)}
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">To Date</label>
                            <input
                                type="date"
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                                value={filters.date_to}
                                onChange={(e) => handleFilterChange('date_to', e.target.value)}
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Personnel Type</label>
                            <select
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                                value={filters.personnel_type}
                                onChange={(e) => handleFilterChange('personnel_type', e.target.value)}
                            >
                                {personnelTypes.map((type) => (
                                    <option key={type.value} value={type.value}>
                                        {type.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                            <select
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                                value={filters.payment_method}
                                onChange={(e) => handleFilterChange('payment_method', e.target.value)}
                            >
                                {paymentMethods.map((method) => (
                                    <option key={method.value} value={method.value}>
                                        {method.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div className="flex items-end space-x-2">
                            <button
                                onClick={handleGenerateReport}
                                disabled={loading}
                                className="flex-1 bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 disabled:opacity-50"
                            >
                                <FunnelIcon className="h-4 w-4 inline mr-1" />
                                {loading ? 'Loading...' : 'Generate'}
                            </button>
                            <button
                                onClick={exportToCSV}
                                disabled={!transactions.length}
                                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                            >
                                <DocumentArrowDownIcon className="h-4 w-4" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* View Toggle */}
            <div className="flex space-x-4">
                <button
                    onClick={() => setActiveView('summary')}
                    className={`px-4 py-2 rounded-md ${
                        activeView === 'summary'
                            ? 'bg-teal-100 text-teal-700 border-teal-300'
                            : 'bg-white text-gray-500 border-gray-300 hover:text-gray-700'
                    } border`}
                >
                    Summary Report
                </button>
                <button
                    onClick={() => setActiveView('detailed')}
                    className={`px-4 py-2 rounded-md ${
                        activeView === 'detailed'
                            ? 'bg-teal-100 text-teal-700 border-teal-300'
                            : 'bg-white text-gray-500 border-gray-300 hover:text-gray-700'
                    } border`}
                >
                    Detailed Transactions
                </button>
            </div>

            {/* Report Content */}
            {loading ? (
                <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                </div>
            ) : (
                <>
                    {/* Summary View */}
                    {activeView === 'summary' && reportData && (
                        <div className="space-y-6">
                            {/* Summary Cards */}
                            <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
                                <div className="bg-white p-6 rounded-lg shadow">
                                    <div className="flex items-center">
                                        <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-500">Total Paid</p>
                                            <p className="text-2xl font-bold text-gray-900">
                                                ₹{reportData.summary?.total_paid?.toLocaleString() || '0'}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white p-6 rounded-lg shadow">
                                    <div className="flex items-center">
                                        <ChartBarIcon className="h-8 w-8 text-blue-600" />
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-500">Total Transactions</p>
                                            <p className="text-2xl font-bold text-gray-900">
                                                {reportData.summary?.total_transactions || 0}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white p-6 rounded-lg shadow">
                                    <div className="flex items-center">
                                        <CalendarIcon className="h-8 w-8 text-purple-600" />
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-500">Date Range</p>
                                            <p className="text-sm font-bold text-gray-900">
                                                {filters.date_from} to {filters.date_to}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Personnel Type Breakdown */}
                            {reportData.report_data && reportData.report_data.length > 0 ? (
                                <div className="bg-white shadow rounded-lg">
                                    <div className="px-6 py-4 border-b border-gray-200">
                                        <h4 className="text-lg font-medium text-gray-900">Payment Breakdown by Personnel Type</h4>
                                    </div>
                                    <div className="p-6">
                                        <div className="space-y-6">
                                            {reportData.report_data.map((typeData) => (
                                                <div key={typeData.personnel_type} className="border rounded-lg p-4">
                                                    <div className="flex justify-between items-center mb-4">
                                                        <h5 className="text-lg font-medium text-gray-900 capitalize">
                                                            {typeData.personnel_type.replace('_', ' ')} 
                                                            <span className="text-sm text-gray-500 ml-2">
                                                                ({typeData.transaction_count} transactions)
                                                            </span>
                                                        </h5>
                                                        <span className="text-xl font-bold text-teal-600">
                                                            ₹{typeData.total_paid?.toLocaleString() || '0'}
                                                        </span>
                                                    </div>

                                                    {/* Individual Personnel */}
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                        {typeData.personnel && typeData.personnel.map((person, idx) => (
                                                            <div key={idx} className="bg-gray-50 p-3 rounded">
                                                                <div className="flex justify-between items-center">
                                                                    <div>
                                                                        <p className="font-medium text-gray-900">
                                                                            {person.details?.name || 'Unknown'}
                                                                        </p>
                                                                        <p className="text-sm text-gray-500">
                                                                            {person.transactions?.length || 0} payments
                                                                        </p>
                                                                    </div>
                                                                    <span className="font-semibold text-teal-600">
                                                                        ₹{person.total_paid?.toLocaleString() || '0'}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="bg-white shadow rounded-lg p-12 text-center">
                                    <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">No payment data found</h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Try releasing some payments to see report data here.
                                    </p>
                                </div>
                            )}
                        </div>
                    )}

                    {/* Detailed Transactions View */}
                    {activeView === 'detailed' && (
                        <div className="bg-white shadow rounded-lg overflow-hidden">
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h4 className="text-lg font-medium text-gray-900">
                                    Payment Transactions ({transactions.length})
                                </h4>
                            </div>

                            {transactions.length === 0 ? (
                                <div className="text-center py-12">
                                    <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Start by releasing some payments to see transaction history.
                                    </p>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                                                    Date & Time
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                                                    Personnel
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                                                    Type
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                                                    Amount
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                                                    Payment Method
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                                                    Reference
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                                                    Processed By
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {transactions.map((transaction) => (
                                                <tr key={transaction.id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {new Date(transaction.processed_at).toLocaleDateString()}
                                                        <br />
                                                        <span className="text-xs text-gray-500">
                                                            {new Date(transaction.processed_at).toLocaleTimeString()}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {transaction.personnel_name || 'Unknown'}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-teal-100 text-teal-800 capitalize">
                                                            {transaction.personnel_type?.replace('_', ' ') || 'Unknown'}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        ₹{transaction.amount?.toLocaleString() || '0'}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                                                        {transaction.payment_method?.replace('_', ' ') || 'N/A'}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {transaction.reference_number || 'N/A'}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {transaction.processed_by || 'System'}
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default PaymentReports;