import React, { useState, useEffect, useCallback } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import personnelService from '../../services/personnelService';
import toast from 'react-hot-toast';

const StaffManagement = ({ onStatsUpdate }) => {
    const [staff, setStaff] = useState([]);
    const [loading, setLoading] = useState(true);
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedStaff, setSelectedStaff] = useState(null);
    const [modalMode, setModalMode] = useState('create');
    const [filters, setFilters] = useState({
        status: '',
        search: ''
    });

    const fetchStaff = useCallback(async () => {
        try {
            setLoading(true);
            const response = await personnelService.getStaff(filters);
            setStaff(response.staff);
        } catch (error) {
            toast.error('Failed to fetch staff');
        } finally {
            setLoading(false);
        }
    }, [filters]);

    useEffect(() => {
        fetchStaff();
    }, [fetchStaff]);

    const handleCreateStaff = () => {
        setSelectedStaff(null);
        setModalMode('create');
        setModalOpen(true);
    };

    const handleEditStaff = (staffMember) => {
        setSelectedStaff(staffMember);
        setModalMode('edit');
        setModalOpen(true);
    };

    const handleDeleteStaff = async (staffMember) => {
        if (window.confirm(`Are you sure you want to delete "${staffMember.name}"?`)) {
            try {
                await personnelService.deleteStaff(staffMember.id);
                toast.success('Staff member deleted successfully');
                fetchStaff();
                if (onStatsUpdate) onStatsUpdate();
            } catch (error) {
                toast.error(error.message || 'Failed to delete staff member');
            }
        }
    };

    const handleSaveStaff = async (staffData) => {
        if (modalMode === 'create') {
            await personnelService.createStaff(staffData);
        } else {
            await personnelService.updateStaff(selectedStaff.id, staffData);
        }
        fetchStaff();
        if (onStatsUpdate) onStatsUpdate();
    };

    const getStatusColor = (status) => {
        return status === 'active' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800';
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    return (
        <div className="space-y-6">
            {/* Header and Actions */}
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-lg font-medium text-gray-900">Staff Members</h3>
                    <p className="text-sm text-gray-500">Manage staff members and their information</p>
                </div>
                <button
                    onClick={handleCreateStaff}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Staff Member
                </button>
            </div>

            {/* Filters */}
            <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Search Staff
                        </label>
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                value={filters.search}
                                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                placeholder="Search by name, email, or employee ID..."
                            />
                        </div>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Status Filter
                        </label>
                        <select
                            value={filters.status}
                            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                        >
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div className="flex items-end">
                        <button
                            onClick={() => setFilters({ status: '', search: '' })}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                            Clear Filters
                        </button>
                    </div>
                </div>
            </div>

            {/* Staff Table */}
            {loading ? (
                <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                </div>
            ) : staff.length === 0 ? (
                <div className="text-center py-12">
                    <div className="text-gray-500 text-lg mb-2">No staff members found</div>
                    <p className="text-gray-400 mb-4">Get started by adding your first staff member</p>
                    <button
                        onClick={handleCreateStaff}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700"
                    >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Staff Member
                    </button>
                </div>
            ) : (
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Staff Details
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Contact Info
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Wallet Summary
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {staff.map((staffMember) => (
                                    <tr key={staffMember.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4">
                                            <div>
                                                <div className="text-sm font-medium text-gray-900">
                                                    {staffMember.name}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    ID: {staffMember.employee_id}
                                                </div>
                                                <div className="text-xs text-gray-400">
                                                    Joined: {new Date(staffMember.hire_date).toLocaleDateString()}
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="text-sm text-gray-900">{staffMember.email}</div>
                                            <div className="text-sm text-gray-500">{staffMember.phone}</div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="text-sm space-y-1">
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Balance:</span>
                                                    <span className="font-medium">{formatCurrency(staffMember.wallet_balance)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Earned:</span>
                                                    <span className="text-green-600">{formatCurrency(staffMember.total_earned)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Paid:</span>
                                                    <span className="text-blue-600">{formatCurrency(staffMember.total_paid)}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(staffMember.status)}`}>
                                                {staffMember.status}
                                            </span>
                                            {staffMember.last_activity && (
                                                <div className="text-xs text-gray-500 mt-1">
                                                    Last: {new Date(staffMember.last_activity).toLocaleDateString()}
                                                </div>
                                            )}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end space-x-2">
                                                <button
                                                    onClick={() => handleEditStaff(staffMember)}
                                                    className="text-teal-600 hover:text-teal-900 p-1"
                                                    title="Edit Staff Member"
                                                >
                                                    <PencilIcon className="h-4 w-4" />
                                                </button>
                                                <button
                                                    onClick={() => handleDeleteStaff(staffMember)}
                                                    className="text-red-600 hover:text-red-900 p-1"
                                                    title="Delete Staff Member"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {/* Staff Modal */}
            {modalOpen && (
                <StaffModal
                    isOpen={modalOpen}
                    onClose={() => setModalOpen(false)}
                    onSave={handleSaveStaff}
                    staff={selectedStaff}
                    mode={modalMode}
                />
            )}
        </div>
    );
};

// StaffModal Component
const StaffModal = ({ isOpen, onClose, onSave, staff, mode }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        employee_id: '',
        status: 'active',
        hire_date: ''
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (staff && mode === 'edit') {
            setFormData({
                name: staff.name || '',
                email: staff.email || '',
                phone: staff.phone || '',
                employee_id: staff.employee_id || '',
                status: staff.status || 'active',
                hire_date: staff.hire_date || ''
            });
        } else {
            setFormData({
                name: '',
                email: '',
                phone: '',
                employee_id: '',
                status: 'active',
                hire_date: new Date().toISOString().split('T')[0]
            });
        }
        setErrors({});
    }, [staff, mode, isOpen]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email is invalid';
        }

        if (!formData.phone.trim()) {
            newErrors.phone = 'Phone is required';
        }

        if (!formData.employee_id.trim()) {
            newErrors.employee_id = 'Employee ID is required';
        }

        if (!formData.hire_date) {
            newErrors.hire_date = 'Hire date is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            await onSave(formData);
            toast.success(mode === 'create' ? 'Staff member created successfully' : 'Staff member updated successfully');
            onClose();
        } catch (error) {
            toast.error(error.message || 'Failed to save staff member');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        {mode === 'create' ? 'Add Staff Member' : 'Edit Staff Member'}
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Full Name *
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.name ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="Enter full name"
                            />
                            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Employee ID *
                            </label>
                            <input
                                type="text"
                                name="employee_id"
                                value={formData.employee_id}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.employee_id ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="e.g., EMP001"
                            />
                            {errors.employee_id && <p className="text-red-500 text-xs mt-1">{errors.employee_id}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Email Address *
                            </label>
                            <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.email ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="Enter email address"
                            />
                            {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Phone Number *
                            </label>
                            <input
                                type="tel"
                                name="phone"
                                value={formData.phone}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.phone ? 'border-red-300' : 'border-gray-300'
                                }`}
                                placeholder="+91 9876543210"
                            />
                            {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Hire Date *
                            </label>
                            <input
                                type="date"
                                name="hire_date"
                                value={formData.hire_date}
                                onChange={handleChange}
                                className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500 ${
                                    errors.hire_date ? 'border-red-300' : 'border-gray-300'
                                }`}
                            />
                            {errors.hire_date && <p className="text-red-500 text-xs mt-1">{errors.hire_date}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Status
                            </label>
                            <select
                                name="status"
                                value={formData.status}
                                onChange={handleChange}
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
                        >
                            {loading ? 'Saving...' : (mode === 'create' ? 'Create Staff Member' : 'Update Staff Member')}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default StaffManagement;
