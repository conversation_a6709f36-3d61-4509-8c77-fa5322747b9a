// Booking Management Service
// Handles booking operations with commission calculations and GST

class BookingService {
    constructor() {
        this.initializeSampleData();
    }

    // Initialize sample data in localStorage
    initializeSampleData() {
        if (!localStorage.getItem('activity_crm_bookings')) {
            const sampleData = {
                bookings: [
                    {
                        id: 1,
                        booking_number: 'BK202500001',
                        guest_name: '<PERSON>',
                        contact_number: '+91 9876543210',
                        booking_date: '2025-01-08',
                        activity_id: 1,
                        activity_name: 'Dinner Cruise',
                        activity_price: 3500,
                        booking_type: 'agency',
                        agency_id: 1,
                        agency_name: 'ABC Tours',
                        staff_id: 1,
                        staff_name: '<PERSON>',
                        boat_boy_id: 1,
                        boat_boy_name: '<PERSON>',
                        commission_breakdown: {
                            agent_commission: 350,
                            staff_commission: 200,
                            boat_boy_commission: 150,
                            total_commission: 700,
                            remaining_amount: 2800,
                            gst_amount: 504,
                            admin_share: 2296
                        },
                        status: 'confirmed',
                        created_at: '2025-01-06T10:30:00Z',
                        created_by: 1
                    },
                    {
                        id: 2,
                        booking_number: 'BK202500002',
                        guest_name: '<PERSON>',
                        contact_number: '+91 9876543211',
                        booking_date: '2025-01-09',
                        activity_id: 3,
                        activity_name: 'Scuba Diving Experience',
                        activity_price: 2500,
                        booking_type: 'direct',
                        agency_id: null,
                        agency_name: null,
                        staff_id: 2,
                        staff_name: 'Jane Smith',
                        boat_boy_id: 2,
                        boat_boy_name: 'Suresh Patel',
                        commission_breakdown: {
                            agent_commission: 0,
                            staff_commission: 150,
                            boat_boy_commission: 100,
                            total_commission: 250,
                            remaining_amount: 2250,
                            gst_amount: 405,
                            admin_share: 1845
                        },
                        status: 'confirmed',
                        created_at: '2025-01-06T14:15:00Z',
                        created_by: 2
                    }
                ],
                agencies: [
                    {
                        id: 1,
                        name: 'ABC Tours',
                        contact_person: 'Rajesh Kumar',
                        email: '<EMAIL>',
                        phone: '+91 9876543230',
                        status: 'active'
                    },
                    {
                        id: 2,
                        name: 'XYZ Travel',
                        contact_person: 'Priya Sharma',
                        email: '<EMAIL>',
                        phone: '+91 9876543231',
                        status: 'active'
                    }
                ],
                next_booking_id: 3,
                next_agency_id: 3
            };
            localStorage.setItem('activity_crm_bookings', JSON.stringify(sampleData));
        }
    }

    // Get all booking data from localStorage
    getBookingData() {
        const data = localStorage.getItem('activity_crm_bookings');
        return data ? JSON.parse(data) : { 
            bookings: [], 
            agencies: [],
            next_booking_id: 1,
            next_agency_id: 1
        };
    }

    // Update booking data in localStorage
    updateBookingData(data) {
        localStorage.setItem('activity_crm_bookings', JSON.stringify(data));
    }

    // Simulate API delay
    delay(ms = 500) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Generate booking number
    generateBookingNumber() {
        const data = this.getBookingData();
        const year = new Date().getFullYear();
        const bookingNumber = `BK${year}${String(data.next_booking_id).padStart(5, '0')}`;
        return bookingNumber;
    }

    // Calculate commission breakdown
    calculateCommissions(activity, bookingType, gstRate = 18, numberOfAdults = 1, numberOfChildren = 0) {
        // Calculate total price based on adults and children
        const adultPrice = activity.price * numberOfAdults;
        const childPrice = (activity.child_price || 0) * numberOfChildren;
        const totalPrice = adultPrice + childPrice;

        // Calculate adult commissions
        const adultCommissions = {
            agent_commission: (bookingType === 'agency' ? activity.commission_structure.agent_commission : 0) * numberOfAdults,
            staff_commission: activity.commission_structure.staff_commission * numberOfAdults,
            boat_boy_commission: activity.commission_structure.boat_boy_commission * numberOfAdults
        };

        // Calculate child commissions if child pricing exists
        const childCommissions = {
            agent_commission: 0,
            staff_commission: 0,
            boat_boy_commission: 0
        };

        if (numberOfChildren > 0 && activity.child_commission_structure) {
            childCommissions.agent_commission = (bookingType === 'agency' ? activity.child_commission_structure.agent_commission : 0) * numberOfChildren;
            childCommissions.staff_commission = activity.child_commission_structure.staff_commission * numberOfChildren;
            childCommissions.boat_boy_commission = activity.child_commission_structure.boat_boy_commission * numberOfChildren;
        }

        // Total commissions
        const commissions = {
            agent_commission: adultCommissions.agent_commission + childCommissions.agent_commission,
            staff_commission: adultCommissions.staff_commission + childCommissions.staff_commission,
            boat_boy_commission: adultCommissions.boat_boy_commission + childCommissions.boat_boy_commission
        };

        const totalCommission = commissions.agent_commission + commissions.staff_commission + commissions.boat_boy_commission;
        const remainingAmount = totalPrice - totalCommission;
        const gstAmount = (remainingAmount * gstRate) / 100;
        const adminShare = remainingAmount - gstAmount;

        return {
            ...commissions,
            total_price: totalPrice,
            adult_price: adultPrice,
            child_price: childPrice,
            total_commission: totalCommission,
            remaining_amount: remainingAmount,
            gst_amount: gstAmount,
            admin_share: adminShare
        };
    }

    // Get all bookings
    async getBookings(filters = {}) {
        await this.delay();
        const data = this.getBookingData();
        let bookings = data.bookings;

        // Apply filters
        if (filters.status) {
            bookings = bookings.filter(booking => booking.status === filters.status);
        }
        if (filters.booking_type) {
            bookings = bookings.filter(booking => booking.booking_type === filters.booking_type);
        }
        if (filters.date_from) {
            bookings = bookings.filter(booking => booking.booking_date >= filters.date_from);
        }
        if (filters.date_to) {
            bookings = bookings.filter(booking => booking.booking_date <= filters.date_to);
        }
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            bookings = bookings.filter(booking => 
                booking.guest_name.toLowerCase().includes(searchTerm) ||
                booking.booking_number.toLowerCase().includes(searchTerm) ||
                booking.activity_name.toLowerCase().includes(searchTerm)
            );
        }

        return {
            bookings,
            total: bookings.length,
            total_revenue: bookings.reduce((sum, booking) => sum + booking.activity_price, 0),
            agency_bookings: bookings.filter(b => b.booking_type === 'agency').length,
            direct_bookings: bookings.filter(b => b.booking_type === 'direct').length
        };
    }

    // Create new booking
    async createBooking(bookingData, currentUser) {
        await this.delay();
        const data = this.getBookingData();

        // Get activity details for commission calculation
        // Note: In a real app, this would be passed as parameter or fetched differently
        const activityData = localStorage.getItem('activity_crm_activities');
        const activities = activityData ? JSON.parse(activityData).activities : [];
        const activity = activities.find(a => a.id === parseInt(bookingData.activity_id));

        if (!activity) {
            throw new Error('Activity not found');
        }

        // Calculate commissions
        const commissionBreakdown = this.calculateCommissions(
            activity,
            bookingData.booking_type,
            18,
            bookingData.number_of_adults || 1,
            bookingData.number_of_children || 0
        );

        const newBooking = {
            id: data.next_booking_id,
            booking_number: this.generateBookingNumber(),
            guest_name: bookingData.guest_name,
            contact_number: bookingData.contact_number,
            booking_date: bookingData.booking_date,
            activity_id: activity.id,
            activity_name: activity.name,
            activity_price: commissionBreakdown.total_price, // Use calculated total price
            adult_price: commissionBreakdown.adult_price,
            child_price: commissionBreakdown.child_price,
            number_of_adults: bookingData.number_of_adults || 1,
            number_of_children: bookingData.number_of_children || 0,
            booking_type: bookingData.booking_type,
            agency_id: bookingData.agency_id || null,
            agency_name: bookingData.agency_name || null,
            staff_id: currentUser.id,
            staff_name: currentUser.name,
            boat_boy_id: bookingData.boat_boy_id,
            boat_boy_name: bookingData.boat_boy_name,
            commission_breakdown: commissionBreakdown,
            status: 'confirmed',
            created_at: new Date().toISOString(),
            created_by: currentUser.id
        };

        data.bookings.push(newBooking);
        data.next_booking_id += 1;
        this.updateBookingData(data);

        // Update wallet balances
        await this.updateWalletBalances(newBooking);

        return newBooking;
    }

    // Update wallet balances after booking
    async updateWalletBalances(booking) {
        try {
            // Update agent wallet if it's an agency booking
            if (booking.booking_type === 'agency' && booking.commission_breakdown.agent_commission > 0) {
                const agentWalletService = await import('./agentWalletService');
                await agentWalletService.default.addCommissionFromBooking(booking);
            }

            // Log commission allocations for debugging
            console.log('Commission allocations for booking:', booking.booking_number, {
                agent: booking.commission_breakdown.agent_commission,
                staff: booking.commission_breakdown.staff_commission,
                boat_boy: booking.commission_breakdown.boat_boy_commission,
                admin: booking.commission_breakdown.admin_share
            });
        } catch (error) {
            console.error('Error updating wallet balances:', error);
        }
    }

    // Get agencies for dropdown (now using agents from personnel service)
    async getAgencies() {
        await this.delay();
        // Import personnel service to get active agents
        const personnelService = require('./personnelService').default;
        const agents = await personnelService.getActiveAgents();

        // Convert agents to agency format for backward compatibility
        return agents.map(agent => ({
            id: agent.id,
            name: agent.name,
            code: agent.agent_code,
            status: 'active'
        }));
    }

    // Get booking statistics
    async getBookingStats() {
        await this.delay();
        const data = this.getBookingData();
        const bookings = data.bookings;

        const today = new Date().toISOString().split('T')[0];
        const thisMonth = new Date().toISOString().slice(0, 7);

        return {
            total_bookings: bookings.length,
            today_bookings: bookings.filter(b => b.booking_date === today).length,
            month_bookings: bookings.filter(b => b.booking_date.startsWith(thisMonth)).length,
            total_revenue: bookings.reduce((sum, b) => sum + b.activity_price, 0),
            month_revenue: bookings
                .filter(b => b.booking_date.startsWith(thisMonth))
                .reduce((sum, b) => sum + b.activity_price, 0),
            agency_bookings: bookings.filter(b => b.booking_type === 'agency').length,
            direct_bookings: bookings.filter(b => b.booking_type === 'direct').length
        };
    }

    // Update booking status
    async updateBookingStatus(id, status) {
        await this.delay();
        const data = this.getBookingData();
        const bookingIndex = data.bookings.findIndex(b => b.id === parseInt(id));
        
        if (bookingIndex === -1) {
            throw new Error('Booking not found');
        }

        data.bookings[bookingIndex].status = status;
        this.updateBookingData(data);

        return data.bookings[bookingIndex];
    }
}

const bookingService = new BookingService();
export default bookingService;
