import React, { useState } from 'react';
import walletService from '../../services/walletService';
import { XMarkIcon } from '@heroicons/react/24/outline';

const PaymentModal = ({ wallet, personnelType, onSuccess, onClose }) => {
    const [formData, setFormData] = useState({
        amount: '',
        payment_method: 'bank_transfer',
        reference_number: '',
        notes: ''
    });
    const [processing, setProcessing] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.amount || parseFloat(formData.amount) <= 0) {
            alert('Please enter a valid amount');
            return;
        }

        if (parseFloat(formData.amount) > wallet.balance) {
            alert('Amount cannot exceed wallet balance');
            return;
        }

        setProcessing(true);
        try {
            await walletService.releasePayment({
                wallet_id: wallet.id,
                ...formData,
                amount: parseFloat(formData.amount),
                personnel_type: personnelType
            });

            onSuccess();
        } catch (error) {
            console.error('Payment release failed:', error);
            alert(error.message || 'Failed to release payment');
        } finally {
            setProcessing(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const paymentMethods = [
        { value: 'bank_transfer', label: 'Bank Transfer' },
        { value: 'cash', label: 'Cash' },
        { value: 'cheque', label: 'Cheque' },
        { value: 'upi', label: 'UPI' }
    ];

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        Release Payment
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                {/* Demo Notice */}
                <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-400">
                    <p className="text-sm text-blue-700">
                        <strong>Demo Mode:</strong> This will update local data only.
                    </p>
                </div>

                {/* Personnel Info */}
                <div className="mb-6 p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-teal-100 flex items-center justify-center">
                                <span className="text-sm font-medium text-teal-700">
                                    {wallet.name?.charAt(0).toUpperCase()}
                                </span>
                            </div>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">{wallet.name}</p>
                            <p className="text-sm text-gray-500">{wallet.email}</p>
                            <p className="text-xs text-green-600">
                                Available: ₹{wallet.balance?.toLocaleString() || '0'}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Payment Form */}
                <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Amount */}
                    <div>
                        <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                            Payment Amount <span className="text-red-500">*</span>
                        </label>
                        <div className="mt-1 relative rounded-md shadow-sm">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span className="text-gray-500 sm:text-sm">₹</span>
                            </div>
                            <input
                                type="number"
                                name="amount"
                                id="amount"
                                className="focus:ring-teal-500 focus:border-teal-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                                placeholder="0.00"
                                value={formData.amount}
                                onChange={handleInputChange}
                                max={wallet.balance}
                                step="0.01"
                                required
                            />
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                            Maximum: ₹{wallet.balance?.toLocaleString() || '0'}
                        </p>
                    </div>

                    {/* Payment Method */}
                    <div>
                        <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700">
                            Payment Method <span className="text-red-500">*</span>
                        </label>
                        <select
                            name="payment_method"
                            id="payment_method"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm rounded-md"
                            value={formData.payment_method}
                            onChange={handleInputChange}
                            required
                        >
                            {paymentMethods.map((method) => (
                                <option key={method.value} value={method.value}>
                                    {method.label}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Reference Number */}
                    <div>
                        <label htmlFor="reference_number" className="block text-sm font-medium text-gray-700">
                            Reference Number
                        </label>
                        <input
                            type="text"
                            name="reference_number"
                            id="reference_number"
                            className="mt-1 focus:ring-teal-500 focus:border-teal-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            placeholder="Transaction ID, Cheque No, etc."
                            value={formData.reference_number}
                            onChange={handleInputChange}
                        />
                    </div>

                    {/* Notes */}
                    <div>
                        <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                            Notes
                        </label>
                        <textarea
                            name="notes"
                            id="notes"
                            rows={3}
                            className="mt-1 shadow-sm focus:ring-teal-500 focus:border-teal-500 block w-full sm:text-sm border border-gray-300 rounded-md"
                            placeholder="Additional notes (optional)"
                            value={formData.notes}
                            onChange={handleInputChange}
                        />
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="flex-1 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                            disabled={processing}
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="flex-1 bg-teal-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
                            disabled={processing}
                        >
                            {processing ? 'Processing...' : 'Release Payment'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default PaymentModal;