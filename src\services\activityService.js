// Activity Management Service
// Handles CRUD operations for activities, pricing, and commission structure

class ActivityService {
    constructor() {
        this.initializeSampleData();
    }

    // Initialize sample data in localStorage
    initializeSampleData() {
        if (!localStorage.getItem('activity_crm_activities')) {
            const sampleData = {
                activities: [
                    {
                        id: 1,
                        name: 'Dinner Cruise',
                        description: 'Romantic dinner cruise with live music and gourmet dining',
                        price: 3500,
                        status: 'active',
                        commission_structure: {
                            agent_commission: 350,
                            staff_commission: 200,
                            boat_boy_commission: 150
                        },
                        total_commission: 700,
                        admin_share: 2800,
                        gst_rate: 18,
                        created_at: '2025-01-01',
                        updated_at: '2025-01-01'
                    },
                    {
                        id: 2,
                        name: 'Jet Ski Adventure',
                        description: 'High-speed jet ski adventure with safety equipment',
                        price: 1000,
                        status: 'active',
                        commission_structure: {
                            agent_commission: 100,
                            staff_commission: 80,
                            boat_boy_commission: 70
                        },
                        total_commission: 250,
                        admin_share: 750,
                        gst_rate: 18,
                        created_at: '2025-01-01',
                        updated_at: '2025-01-01'
                    },
                    {
                        id: 3,
                        name: 'Scuba Diving Experience',
                        description: 'Professional scuba diving with certified instructors',
                        price: 2500,
                        status: 'active',
                        commission_structure: {
                            agent_commission: 250,
                            staff_commission: 150,
                            boat_boy_commission: 100
                        },
                        total_commission: 500,
                        admin_share: 2000,
                        gst_rate: 18,
                        created_at: '2025-01-01',
                        updated_at: '2025-01-01'
                    },
                    {
                        id: 4,
                        name: 'Snorkeling Tour',
                        description: 'Guided snorkeling tour in crystal clear waters',
                        price: 1500,
                        status: 'active',
                        commission_structure: {
                            agent_commission: 150,
                            staff_commission: 100,
                            boat_boy_commission: 80
                        },
                        total_commission: 330,
                        admin_share: 1170,
                        gst_rate: 18,
                        created_at: '2025-01-01',
                        updated_at: '2025-01-01'
                    },
                    {
                        id: 5,
                        name: 'Island Hopping',
                        description: 'Visit multiple beautiful islands in one day',
                        price: 2800,
                        status: 'active',
                        commission_structure: {
                            agent_commission: 280,
                            staff_commission: 180,
                            boat_boy_commission: 140
                        },
                        total_commission: 600,
                        admin_share: 2200,
                        gst_rate: 18,
                        created_at: '2025-01-01',
                        updated_at: '2025-01-01'
                    },
                    {
                        id: 6,
                        name: 'Parasailing Adventure',
                        description: 'Soar high above the ocean with parasailing',
                        price: 1800,
                        status: 'inactive',
                        commission_structure: {
                            agent_commission: 180,
                            staff_commission: 120,
                            boat_boy_commission: 100
                        },
                        total_commission: 400,
                        admin_share: 1400,
                        gst_rate: 18,
                        created_at: '2025-01-01',
                        updated_at: '2025-01-01'
                    }
                ],
                next_id: 7
            };
            localStorage.setItem('activity_crm_activities', JSON.stringify(sampleData));
        }
    }

    // Get all activities data from localStorage
    getActivitiesData() {
        const data = localStorage.getItem('activity_crm_activities');
        return data ? JSON.parse(data) : { activities: [], next_id: 1 };
    }

    // Update activities data in localStorage
    updateActivitiesData(data) {
        localStorage.setItem('activity_crm_activities', JSON.stringify(data));
    }

    // Simulate API delay
    delay(ms = 500) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get all activities
    async getActivities(filters = {}) {
        await this.delay();
        const data = this.getActivitiesData();
        let activities = data.activities;

        // Apply filters
        if (filters.status) {
            activities = activities.filter(activity => activity.status === filters.status);
        }
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            activities = activities.filter(activity => 
                activity.name.toLowerCase().includes(searchTerm) ||
                activity.description.toLowerCase().includes(searchTerm)
            );
        }

        return {
            activities,
            total: activities.length,
            active_count: data.activities.filter(a => a.status === 'active').length,
            inactive_count: data.activities.filter(a => a.status === 'inactive').length
        };
    }

    // Get single activity by ID
    async getActivity(id) {
        await this.delay();
        const data = this.getActivitiesData();
        const activity = data.activities.find(a => a.id === parseInt(id));
        if (!activity) {
            throw new Error('Activity not found');
        }
        return activity;
    }

    // Create new activity
    async createActivity(activityData) {
        await this.delay();
        const data = this.getActivitiesData();
        
        // Validate commission structure
        const totalCommission = 
            activityData.commission_structure.agent_commission +
            activityData.commission_structure.staff_commission +
            activityData.commission_structure.boat_boy_commission;

        if (totalCommission > activityData.price) {
            throw new Error('Total commission cannot exceed activity price');
        }

        const newActivity = {
            id: data.next_id,
            ...activityData,
            total_commission: totalCommission,
            admin_share: activityData.price - totalCommission,
            created_at: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.activities.push(newActivity);
        data.next_id += 1;
        this.updateActivitiesData(data);

        return newActivity;
    }

    // Update existing activity
    async updateActivity(id, activityData) {
        await this.delay();
        const data = this.getActivitiesData();
        const activityIndex = data.activities.findIndex(a => a.id === parseInt(id));
        
        if (activityIndex === -1) {
            throw new Error('Activity not found');
        }

        // Validate commission structure if provided
        if (activityData.commission_structure) {
            const totalCommission = 
                activityData.commission_structure.agent_commission +
                activityData.commission_structure.staff_commission +
                activityData.commission_structure.boat_boy_commission;

            const price = activityData.price || data.activities[activityIndex].price;
            if (totalCommission > price) {
                throw new Error('Total commission cannot exceed activity price');
            }

            activityData.total_commission = totalCommission;
            activityData.admin_share = price - totalCommission;
        }

        const updatedActivity = {
            ...data.activities[activityIndex],
            ...activityData,
            updated_at: new Date().toISOString().split('T')[0]
        };

        data.activities[activityIndex] = updatedActivity;
        this.updateActivitiesData(data);

        return updatedActivity;
    }

    // Delete activity
    async deleteActivity(id) {
        await this.delay();
        const data = this.getActivitiesData();
        const activityIndex = data.activities.findIndex(a => a.id === parseInt(id));
        
        if (activityIndex === -1) {
            throw new Error('Activity not found');
        }

        data.activities.splice(activityIndex, 1);
        this.updateActivitiesData(data);

        return { success: true, message: 'Activity deleted successfully' };
    }

    // Toggle activity status
    async toggleActivityStatus(id) {
        await this.delay();
        const data = this.getActivitiesData();
        const activityIndex = data.activities.findIndex(a => a.id === parseInt(id));
        
        if (activityIndex === -1) {
            throw new Error('Activity not found');
        }

        const currentStatus = data.activities[activityIndex].status;
        data.activities[activityIndex].status = currentStatus === 'active' ? 'inactive' : 'active';
        data.activities[activityIndex].updated_at = new Date().toISOString().split('T')[0];

        this.updateActivitiesData(data);

        return data.activities[activityIndex];
    }

    // Get activities for dropdown (active only)
    async getActiveActivities() {
        await this.delay();
        const data = this.getActivitiesData();
        return data.activities
            .filter(activity => activity.status === 'active')
            .map(activity => ({
                id: activity.id,
                name: activity.name,
                price: activity.price,
                commission_structure: activity.commission_structure
            }));
    }
}

const activityService = new ActivityService();
export default activityService;
