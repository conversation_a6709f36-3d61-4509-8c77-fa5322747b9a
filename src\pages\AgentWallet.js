import React, { useState, useEffect, useCallback } from 'react';
import { 
    CurrencyDollarIcon, 
    ChartBarIcon, 
    CalendarIcon,
    ArrowTrendingUpIcon,
    BanknotesIcon,
    ClockIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import agentWalletService from '../services/agentWalletService';
import toast from 'react-hot-toast';

const AgentWallet = () => {
    const { user } = useAuth();
    const [, setWallet] = useState(null);
    const [stats, setStats] = useState(null);
    const [transactions, setTransactions] = useState([]);
    const [recentTransactions, setRecentTransactions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('overview');
    const [filters, setFilters] = useState({
        transaction_type: '',
        status: '',
        date_from: '',
        date_to: ''
    });

    const fetchWalletData = useCallback(async () => {
        if (!user?.id) return;

        try {
            setLoading(true);
            const [walletData, statsData, recentData] = await Promise.all([
                agentWalletService.getAgentWallet(user.id),
                agentWalletService.getWalletStats(user.id),
                agentWalletService.getRecentTransactions(user.id, 5)
            ]);

            setWallet(walletData);
            setStats(statsData);
            setRecentTransactions(recentData);
        } catch (error) {
            toast.error('Failed to fetch wallet data');
        } finally {
            setLoading(false);
        }
    }, [user?.id]);

    const fetchTransactions = useCallback(async () => {
        if (!user?.id) return;

        try {
            const response = await agentWalletService.getAgentTransactions(user.id, filters);
            setTransactions(response.transactions);
        } catch (error) {
            toast.error('Failed to fetch transactions');
        }
    }, [user?.id, filters]);

    useEffect(() => {
        if (user?.id) {
            fetchWalletData();
        }
    }, [user, fetchWalletData]);

    useEffect(() => {
        if (user?.id && activeTab === 'transactions') {
            fetchTransactions();
        }
    }, [user, activeTab, filters, fetchTransactions]);

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    const getTransactionTypeColor = (type) => {
        return type === 'commission_earned' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-blue-100 text-blue-800';
    };

    const getTransactionIcon = (type) => {
        return type === 'commission_earned' ? '💰' : '💳';
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div>
                <h1 className="text-2xl font-bold text-gray-900">My Wallet</h1>
                <p className="mt-2 text-sm text-gray-700">
                    Track your commission earnings and payment history
                </p>
            </div>

            {/* Stats Cards */}
            {stats && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl shadow-sm p-6 border border-green-200">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-green-700">Current Balance</p>
                                <p className="text-2xl font-bold text-green-900">
                                    {formatCurrency(stats.current_balance)}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl shadow-sm p-6 border border-blue-200">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ArrowTrendingUpIcon className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-blue-700">Total Earned</p>
                                <p className="text-2xl font-bold text-blue-900">
                                    {formatCurrency(stats.total_earned)}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl shadow-sm p-6 border border-purple-200">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <CalendarIcon className="h-8 w-8 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-purple-700">This Month</p>
                                <p className="text-2xl font-bold text-purple-900">
                                    {formatCurrency(stats.month_commission)}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-xl shadow-sm p-6 border border-orange-200">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ChartBarIcon className="h-8 w-8 text-orange-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-orange-700">Total Bookings</p>
                                <p className="text-2xl font-bold text-orange-900">{stats.total_bookings}</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Tab Navigation */}
            <div className="bg-white shadow rounded-lg">
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button
                            onClick={() => setActiveTab('overview')}
                            className={`${
                                activeTab === 'overview'
                                    ? 'border-teal-500 text-teal-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                        >
                            📊 Overview
                        </button>
                        <button
                            onClick={() => setActiveTab('transactions')}
                            className={`${
                                activeTab === 'transactions'
                                    ? 'border-teal-500 text-teal-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                        >
                            📋 All Transactions
                        </button>
                    </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                    {activeTab === 'overview' && (
                        <div className="space-y-6">
                            {/* Recent Transactions */}
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Transactions</h3>
                                {recentTransactions.length === 0 ? (
                                    <div className="text-center py-8">
                                        <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions yet</h3>
                                        <p className="mt-1 text-sm text-gray-500">
                                            Your commission transactions will appear here.
                                        </p>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {recentTransactions.map((transaction) => (
                                            <div key={transaction.id} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                                                <div className="flex items-center">
                                                    <div className="text-2xl mr-3">
                                                        {getTransactionIcon(transaction.transaction_type)}
                                                    </div>
                                                    <div>
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {transaction.transaction_type === 'commission_earned' 
                                                                ? `Commission from ${transaction.activity_name}`
                                                                : 'Payment Received'
                                                            }
                                                        </div>
                                                        <div className="text-xs text-gray-500">
                                                            {new Date(transaction.created_at).toLocaleDateString()} • 
                                                            {transaction.booking_number && ` Booking: ${transaction.booking_number}`}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="text-sm font-semibold text-green-600">
                                                        +{formatCurrency(transaction.commission_amount)}
                                                    </div>
                                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTransactionTypeColor(transaction.transaction_type)}`}>
                                                        {transaction.transaction_type.replace('_', ' ')}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            {/* Quick Actions */}
                            <div className="bg-gradient-to-r from-teal-50 to-blue-50 rounded-lg p-6 border border-teal-200">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">💼 Quick Actions</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <button className="flex items-center p-4 bg-white rounded-lg border border-gray-200 hover:border-teal-300 transition-colors">
                                        <BanknotesIcon className="h-6 w-6 text-teal-600 mr-3" />
                                        <div className="text-left">
                                            <div className="text-sm font-medium text-gray-900">Request Payment</div>
                                            <div className="text-xs text-gray-500">Withdraw your earnings</div>
                                        </div>
                                    </button>
                                    <button 
                                        onClick={() => setActiveTab('transactions')}
                                        className="flex items-center p-4 bg-white rounded-lg border border-gray-200 hover:border-teal-300 transition-colors"
                                    >
                                        <ChartBarIcon className="h-6 w-6 text-teal-600 mr-3" />
                                        <div className="text-left">
                                            <div className="text-sm font-medium text-gray-900">View All Transactions</div>
                                            <div className="text-xs text-gray-500">Complete transaction history</div>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'transactions' && (
                        <div className="space-y-6">
                            {/* Filters */}
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Transaction Type
                                        </label>
                                        <select
                                            value={filters.transaction_type}
                                            onChange={(e) => setFilters(prev => ({ ...prev, transaction_type: e.target.value }))}
                                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                        >
                                            <option value="">All Types</option>
                                            <option value="commission_earned">Commission Earned</option>
                                            <option value="payment_received">Payment Received</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Date From
                                        </label>
                                        <input
                                            type="date"
                                            value={filters.date_from}
                                            onChange={(e) => setFilters(prev => ({ ...prev, date_from: e.target.value }))}
                                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Date To
                                        </label>
                                        <input
                                            type="date"
                                            value={filters.date_to}
                                            onChange={(e) => setFilters(prev => ({ ...prev, date_to: e.target.value }))}
                                            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                        />
                                    </div>

                                    <div className="flex items-end">
                                        <button
                                            onClick={() => setFilters({ transaction_type: '', status: '', date_from: '', date_to: '' })}
                                            className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                                        >
                                            Clear Filters
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Transactions Table */}
                            <div className="bg-white shadow overflow-hidden sm:rounded-md">
                                {transactions.length === 0 ? (
                                    <div className="text-center py-12">
                                        <div className="text-gray-500 text-lg mb-2">No transactions found</div>
                                        <p className="text-gray-400">Try adjusting your filters or check back later</p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <table className="min-w-full divide-y divide-gray-200">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Transaction Details
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Booking Info
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Amount
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Date
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-white divide-y divide-gray-200">
                                                {transactions.map((transaction) => (
                                                    <tr key={transaction.id} className="hover:bg-gray-50">
                                                        <td className="px-6 py-4">
                                                            <div className="flex items-center">
                                                                <div className="text-2xl mr-3">
                                                                    {getTransactionIcon(transaction.transaction_type)}
                                                                </div>
                                                                <div>
                                                                    <div className="text-sm font-medium text-gray-900">
                                                                        {transaction.transaction_type === 'commission_earned' 
                                                                            ? 'Commission Earned'
                                                                            : 'Payment Received'
                                                                        }
                                                                    </div>
                                                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTransactionTypeColor(transaction.transaction_type)}`}>
                                                                        {transaction.transaction_type.replace('_', ' ')}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4">
                                                            {transaction.booking_number ? (
                                                                <div>
                                                                    <div className="text-sm font-medium text-gray-900">{transaction.activity_name}</div>
                                                                    <div className="text-sm text-gray-500">Guest: {transaction.guest_name}</div>
                                                                    <div className="text-xs text-gray-400">#{transaction.booking_number}</div>
                                                                </div>
                                                            ) : (
                                                                <div className="text-sm text-gray-500">
                                                                    {transaction.payment_method && `via ${transaction.payment_method}`}
                                                                    {transaction.payment_reference && (
                                                                        <div className="text-xs text-gray-400">Ref: {transaction.payment_reference}</div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </td>
                                                        <td className="px-6 py-4">
                                                            <div className="text-sm font-semibold text-green-600">
                                                                +{formatCurrency(transaction.commission_amount)}
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4">
                                                            <div className="text-sm text-gray-900">
                                                                {new Date(transaction.transaction_date).toLocaleDateString()}
                                                            </div>
                                                            <div className="text-xs text-gray-500">
                                                                {new Date(transaction.created_at).toLocaleTimeString()}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default AgentWallet;
