import React, { useState, useEffect } from 'react';
import { XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import slotService from '../../services/slotService';
import ticketService from '../../services/ticketService';
import toast from 'react-hot-toast';

const ParticipantModal = ({ isOpen, onClose, slot, onSuccess }) => {
    const { user } = useAuth();
    const [formData, setFormData] = useState({
        ticket_id: '',
        mode_of_journey: 'via_boat',
        boat_name: '',
        name: '',
        contact: '',
        adults: 1,
        children: 0,
        amount_per_person: 3500
    });
    const [ticketSearchResults, setTicketSearchResults] = useState([]);
    const [showTicketSearch, setShowTicketSearch] = useState(false);
    const [ticketLoading, setTicketLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (isOpen && slot) {
            // Reset form when modal opens
            setFormData({
                name: '',
                contact: '',
                adults: 1,
                children: 0,
                amount_per_person: 3500
            });
            setErrors({});
        }
    }, [isOpen, slot]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }

        // Handle ticket ID search
        if (field === 'ticket_id') {
            handleTicketSearch(value);
        }
    };

    const handleTicketSearch = async (ticketId) => {
        if (!ticketId || ticketId.length < 2) {
            setTicketSearchResults([]);
            setShowTicketSearch(false);
            return;
        }

        try {
            setTicketLoading(true);
            const results = await ticketService.searchTickets(ticketId);
            setTicketSearchResults(results);
            setShowTicketSearch(results.length > 0);
        } catch (error) {
            console.error('Ticket search error:', error);
            setTicketSearchResults([]);
            setShowTicketSearch(false);
        } finally {
            setTicketLoading(false);
        }
    };

    const handleTicketSelect = async (ticket) => {
        try {
            // Validate ticket
            await ticketService.validateTicketForSlot(ticket.id);

            setFormData(prev => ({
                ...prev,
                ticket_id: ticket.id,
                name: ticket.customer_name,
                contact: ticket.contact,
                email: ticket.email || '',
                number_of_adults: ticket.adults,
                number_of_children: ticket.children
            }));

            setShowTicketSearch(false);
            toast.success('Ticket details loaded successfully!');
        } catch (error) {
            toast.error(error.message || 'Failed to load ticket');
        }
    };



    const validateForm = () => {
        const newErrors = {};

        if (formData.mode_of_journey === 'via_boat' && !formData.boat_name.trim()) {
            newErrors.boat_name = 'Boat name is required';
        }

        if (formData.mode_of_journey === 'via_trekking' && !formData.boat_name.trim()) {
            newErrors.boat_name = 'Guide name is required';
        }

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        if (!formData.contact.trim()) {
            newErrors.contact = 'Contact number is required';
        } else if (!/^\+?[\d\s-()]+$/.test(formData.contact)) {
            newErrors.contact = 'Invalid contact number format';
        }

        const adults = parseInt(formData.adults);
        if (!formData.adults || isNaN(adults) || adults < 1) {
            newErrors.adults = 'At least 1 adult is required';
        }

        const children = parseInt(formData.children);
        if (formData.children && (isNaN(children) || children < 0)) {
            newErrors.children = 'Number of children must be 0 or greater';
        }

        const totalPersons = adults + children;
        if (slot && totalPersons > slot.available_count) {
            newErrors.total_persons = `Cannot add ${totalPersons} persons. Only ${slot.available_count} spots available.`;
        }

        const amountPerPerson = parseFloat(formData.amount_per_person);
        if (!formData.amount_per_person || isNaN(amountPerPerson) || amountPerPerson <= 0) {
            newErrors.amount_per_person = 'Valid amount per person is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            const participantData = {
                ...formData,
                adults: parseInt(formData.adults),
                children: parseInt(formData.children),
                amount_per_person: parseFloat(formData.amount_per_person)
            };

            await slotService.addParticipant(slot.id, participantData, user);
            toast.success('Participant added successfully!');
            
            if (onSuccess) {
                onSuccess();
            }
            
            resetForm();
        } catch (error) {
            toast.error(error.message || 'Failed to add participant');
        } finally {
            setLoading(false);
        }
    };

    const resetForm = () => {
        setFormData({
            ticket_id: '',
            mode_of_journey: 'via_boat',
            boat_name: '',
            name: '',
            contact: '',
            email: '',
            number_of_adults: 1,
            number_of_children: 0
        });
        setErrors({});
        setTicketSearchResults([]);
        setShowTicketSearch(false);
    };

    const totalPersons = parseInt(formData.adults || 0) + parseInt(formData.children || 0);
    const totalAmount = totalPersons * parseFloat(formData.amount_per_person || 0);

    if (!isOpen || !slot) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        Add Participant to Slot
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <div className="mb-4 p-3 bg-blue-50 rounded-md">
                    <h4 className="text-sm font-medium text-blue-900 mb-1">{slot.activity_name}</h4>
                    <p className="text-sm text-blue-800">{slot.date} at {slot.time}</p>
                    <p className="text-sm text-blue-800">Available spots: {slot.available_count}/{slot.capacity}</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="relative">
                        <label className="block text-sm font-medium text-gray-700">
                            Ticket ID (Optional)
                        </label>
                        <div className="relative">
                            <input
                                type="text"
                                value={formData.ticket_id}
                                onChange={(e) => handleInputChange('ticket_id', e.target.value.toUpperCase())}
                                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                placeholder="Enter ticket ID (e.g., TKT001)"
                            />
                            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                                {ticketLoading ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600"></div>
                                ) : (
                                    <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                                )}
                            </div>
                        </div>

                        {/* Ticket Search Results */}
                        {showTicketSearch && ticketSearchResults.length > 0 && (
                            <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                {ticketSearchResults.map((ticket) => (
                                    <div
                                        key={ticket.id}
                                        onClick={() => handleTicketSelect(ticket)}
                                        className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                                    >
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <p className="font-medium text-gray-900">{ticket.id}</p>
                                                <p className="text-sm text-gray-600">{ticket.customer_name}</p>
                                                <p className="text-xs text-gray-500">{ticket.contact}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium text-gray-900">{ticket.total_persons} persons</p>
                                                <p className="text-xs text-gray-500">₹{ticket.total_amount.toLocaleString()}</p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        <p className="mt-1 text-xs text-gray-500">
                            Enter ticket ID to auto-fill participant details
                        </p>
                    </div>

                    {/* Main Form Fields - Grid Layout */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Mode of Journey *
                            </label>
                            <select
                                value={formData.mode_of_journey}
                                onChange={(e) => handleInputChange('mode_of_journey', e.target.value)}
                                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                            >
                                <option value="via_boat">Via Boat</option>
                                <option value="via_trekking">Via Trekking</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                {formData.mode_of_journey === 'via_trekking' ? 'Guide Name *' : 'Boat Name *'}
                            </label>
                            <input
                                type="text"
                                value={formData.boat_name}
                                onChange={(e) => handleInputChange('boat_name', e.target.value)}
                                className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                    errors.boat_name ? 'border-red-300' : 'border-gray-300'
                                } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                placeholder={formData.mode_of_journey === 'via_trekking' ? 'e.g., John Doe, Guide-1, etc.' : 'e.g., Semi-Submarine A, Boat-1, etc.'}
                            />
                            {errors.boat_name && <p className="mt-1 text-sm text-red-600">{errors.boat_name}</p>}
                            <p className="mt-1 text-xs text-gray-500">
                                {formData.mode_of_journey === 'via_trekking'
                                    ? 'Specify which guide will accompany this participant (for commission tracking)'
                                    : 'Specify which boat this participant will use (for commission tracking)'
                                }
                            </p>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Lead Person Name *
                            </label>
                            <input
                                type="text"
                                value={formData.name}
                                onChange={(e) => handleInputChange('name', e.target.value)}
                                className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                    errors.name ? 'border-red-300' : 'border-gray-300'
                                } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                placeholder="Enter lead person name"
                            />
                            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                        </div>
                    </div>

                    {/* Contact and Email - Grid Layout */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Contact Number *
                            </label>
                            <input
                                type="tel"
                                value={formData.contact}
                                onChange={(e) => handleInputChange('contact', e.target.value)}
                                className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                    errors.contact ? 'border-red-300' : 'border-gray-300'
                                } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                                placeholder="+91 9876543210"
                            />
                            {errors.contact && <p className="mt-1 text-sm text-red-600">{errors.contact}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Email (Optional)
                            </label>
                            <input
                                type="email"
                                value={formData.email}
                                onChange={(e) => handleInputChange('email', e.target.value)}
                                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                placeholder="<EMAIL>"
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Adults *
                            </label>
                            <input
                                type="number"
                                value={formData.number_of_adults}
                                onChange={(e) => handleInputChange('number_of_adults', e.target.value)}
                                min="1"
                                className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                    errors.adults ? 'border-red-300' : 'border-gray-300'
                                } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                            />
                            {errors.adults && <p className="mt-1 text-sm text-red-600">{errors.adults}</p>}
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Children
                            </label>
                            <input
                                type="number"
                                value={formData.number_of_children}
                                onChange={(e) => handleInputChange('number_of_children', e.target.value)}
                                min="0"
                                className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                    errors.children ? 'border-red-300' : 'border-gray-300'
                                } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                            />
                            {errors.children && <p className="mt-1 text-sm text-red-600">{errors.children}</p>}
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Amount per Person (₹) *
                        </label>
                        <input
                            type="number"
                            value={formData.amount_per_person}
                            onChange={(e) => handleInputChange('amount_per_person', e.target.value)}
                            min="0"
                            step="0.01"
                            className={`mt-1 block w-full border rounded-md px-3 py-2 ${
                                errors.amount_per_person ? 'border-red-300' : 'border-gray-300'
                            } focus:outline-none focus:ring-teal-500 focus:border-teal-500`}
                        />
                        {errors.amount_per_person && <p className="mt-1 text-sm text-red-600">{errors.amount_per_person}</p>}
                    </div>

                    {errors.total_persons && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-600">{errors.total_persons}</p>
                        </div>
                    )}

                    <div className="bg-gray-50 p-3 rounded-md">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Booking Summary</h4>
                        <div className="space-y-1 text-sm text-gray-600">
                            <div className="flex justify-between">
                                <span>Total Persons:</span>
                                <span className="font-medium">{totalPersons}</span>
                            </div>
                            <div className="flex justify-between">
                                <span>Rate per Person:</span>
                                <span className="font-medium">₹{parseFloat(formData.amount_per_person || 0).toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between border-t border-gray-200 pt-1">
                                <span className="font-medium">Total Amount:</span>
                                <span className="font-bold text-gray-900">₹{totalAmount.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 bg-teal-600 text-white rounded-md text-sm font-medium hover:bg-teal-700 disabled:opacity-50"
                        >
                            {loading ? 'Adding...' : 'Add Participant'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default ParticipantModal;
